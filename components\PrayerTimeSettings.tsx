import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  Switch,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { prayerTimeService, PrayerTimeSettings as PrayerSettings, PrayerTimes } from '../services/PrayerTimeService';

interface Props {
  onClose: () => void;
  onSettingsUpdated: () => void;
}

export const PrayerTimeSettings: React.FC<Props> = ({ onClose, onSettingsUpdated }) => {
  const [settings, setSettings] = useState<PrayerSettings>({
    useAutoLocation: true,
    calculationMethod: 'MWL',
    madhab: 'Shafi',
  });
  const [manualTimes, setManualTimes] = useState<PrayerTimes>({
    fajr: '05:30',
    dhuhr: '12:30',
    asr: '15:45',
    maghrib: '18:15',
    isha: '19:45',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<string>('');

  useEffect(() => {
    loadCurrentSettings();
  }, []);

  const loadCurrentSettings = async () => {
    try {
      const currentSettings = prayerTimeService.getSettings();
      setSettings(currentSettings);
      
      if (currentSettings.manualPrayerTimes) {
        setManualTimes(currentSettings.manualPrayerTimes);
      }
      
      // Get current location info
      const location = await prayerTimeService.getCurrentLocation();
      if (location) {
        setCurrentLocation(`${location.city || 'Unknown'}, ${location.country || 'Unknown'}`);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const handleAutoLocationToggle = async (value: boolean) => {
    setSettings(prev => ({ ...prev, useAutoLocation: value }));
    
    if (value) {
      setIsLoading(true);
      try {
        await prayerTimeService.updateSettings({ useAutoLocation: true });
        await prayerTimeService.getCurrentLocation();
        await prayerTimeService.calculatePrayerTimes();
        onSettingsUpdated();
      } catch (error) {
        Alert.alert('Error', 'Failed to get location. Please check your location permissions.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleCalculationMethodChange = async (method: string) => {
    const newSettings = { ...settings, calculationMethod: method as any };
    setSettings(newSettings);
    await prayerTimeService.updateSettings(newSettings);
    
    if (settings.useAutoLocation) {
      await prayerTimeService.calculatePrayerTimes();
      onSettingsUpdated();
    }
  };

  const handleMadhabChange = async (madhab: string) => {
    const newSettings = { ...settings, madhab: madhab as any };
    setSettings(newSettings);
    await prayerTimeService.updateSettings(newSettings);
    
    if (settings.useAutoLocation) {
      await prayerTimeService.calculatePrayerTimes();
      onSettingsUpdated();
    }
  };

  const handleManualTimeChange = (prayer: keyof PrayerTimes, time: string) => {
    setManualTimes(prev => ({ ...prev, [prayer]: time }));
  };

  const saveManualTimes = async () => {
    try {
      await prayerTimeService.setManualPrayerTimes(manualTimes);
      onSettingsUpdated();
      Alert.alert('Success', 'Manual prayer times saved successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to save manual prayer times.');
    }
  };

  const refreshLocation = async () => {
    setIsLoading(true);
    try {
      const location = await prayerTimeService.getCurrentLocation();
      if (location) {
        setCurrentLocation(`${location.city || 'Unknown'}, ${location.country || 'Unknown'}`);
        await prayerTimeService.calculatePrayerTimes();
        onSettingsUpdated();
        Alert.alert('Success', 'Location updated and prayer times recalculated!');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to refresh location.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Prayer Time Settings</Text>
        <Pressable style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>✕</Text>
        </Pressable>
      </View>

      {/* Auto Location Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Location Settings</Text>
        </View>
        
        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Use Auto Location</Text>
          <Switch
            value={settings.useAutoLocation}
            onValueChange={handleAutoLocationToggle}
            trackColor={{ false: '#767577', true: '#4F46E5' }}
            thumbColor={settings.useAutoLocation ? '#FFFFFF' : '#f4f3f4'}
          />
        </View>

        {settings.useAutoLocation && (
          <View style={styles.locationInfo}>
            <Text style={styles.locationText}>Current Location: {currentLocation}</Text>
            <Pressable style={styles.refreshButton} onPress={refreshLocation} disabled={isLoading}>
              {isLoading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.refreshButtonText}>Refresh Location</Text>
              )}
            </Pressable>
          </View>
        )}
      </View>

      {/* Calculation Method Section */}
      {settings.useAutoLocation && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Calculation Method</Text>
          
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={settings.calculationMethod}
              onValueChange={handleCalculationMethodChange}
              style={styles.picker}
            >
              <Picker.Item label="Muslim World League" value="MWL" />
              <Picker.Item label="Islamic Society of North America" value="ISNA" />
              <Picker.Item label="Egyptian General Authority of Survey" value="Egypt" />
              <Picker.Item label="Umm Al-Qura University, Makkah" value="Makkah" />
              <Picker.Item label="University of Islamic Sciences, Karachi" value="Karachi" />
              <Picker.Item label="Institute of Geophysics, University of Tehran" value="Tehran" />
              <Picker.Item label="Shia Ithna-Ashari" value="Jafari" />
            </Picker>
          </View>

          <Text style={styles.sectionTitle}>Madhab</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={settings.madhab}
              onValueChange={handleMadhabChange}
              style={styles.picker}
            >
              <Picker.Item label="Shafi/Maliki/Hanbali" value="Shafi" />
              <Picker.Item label="Hanafi" value="Hanafi" />
            </Picker>
          </View>
        </View>
      )}

      {/* Manual Times Section */}
      {!settings.useAutoLocation && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Manual Prayer Times</Text>
          
          {Object.entries(manualTimes).map(([prayer, time]) => (
            <View key={prayer} style={styles.timeInputRow}>
              <Text style={styles.prayerLabel}>{prayer.charAt(0).toUpperCase() + prayer.slice(1)}</Text>
              <TextInput
                style={styles.timeInput}
                value={time}
                onChangeText={(text) => handleManualTimeChange(prayer as keyof PrayerTimes, text)}
                placeholder="HH:MM"
                maxLength={5}
              />
            </View>
          ))}
          
          <Pressable style={styles.saveButton} onPress={saveManualTimes}>
            <Text style={styles.saveButtonText}>Save Manual Times</Text>
          </Pressable>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1F2937',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#374151',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#F9FAFB',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#374151',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#F9FAFB',
    fontSize: 18,
    fontWeight: 'bold',
  },
  section: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#374151',
  },
  sectionHeader: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#F9FAFB',
    marginBottom: 15,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  settingLabel: {
    fontSize: 16,
    color: '#D1D5DB',
  },
  locationInfo: {
    marginTop: 10,
  },
  locationText: {
    fontSize: 14,
    color: '#9CA3AF',
    marginBottom: 10,
  },
  refreshButton: {
    backgroundColor: '#4F46E5',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  refreshButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  pickerContainer: {
    backgroundColor: '#374151',
    borderRadius: 8,
    marginBottom: 15,
  },
  picker: {
    color: '#F9FAFB',
  },
  timeInputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  prayerLabel: {
    fontSize: 16,
    color: '#D1D5DB',
    flex: 1,
  },
  timeInput: {
    backgroundColor: '#374151',
    color: '#F9FAFB',
    padding: 10,
    borderRadius: 8,
    width: 80,
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: '#10B981',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
