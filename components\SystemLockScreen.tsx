import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Modal,
  StatusBar,
  BackHandler,
  Platform,
  Alert,
  AppState,
  Dimensions,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import {
  useTimerStore,
  useTimerStatus,
  usePrayerContext,
} from '../store/timerStore';
import { TimerDisplay } from './TimerDisplay';
import { useTimer } from '../hooks/useTimer';
import AndroidOverlayService from '../services/AndroidOverlayService';

interface SystemLockScreenProps {
  visible: boolean;
  onExit?: () => void;
}

export const SystemLockScreen: React.FC<SystemLockScreenProps> = ({
  visible,
  onExit,
}) => {
  const [showExitConfirmation, setShowExitConfirmation] = useState(false);
  const [overlayPermissionGranted, setOverlayPermissionGranted] =
    useState(false);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);

  const { stopTimer, recordExitAttempt } = useTimerStore();
  const { isRunning } = useTimerStatus();
  const { currentPrayer, exitAttempts } = usePrayerContext();

  // Initialize timer hook
  useTimer();

  // Animation values
  const exitButtonScale = useSharedValue(1);
  const exitButtonOpacity = useSharedValue(0.7);
  const progressWidth = useSharedValue(0);

  // Handle Android back button
  useEffect(() => {
    if (!visible || Platform.OS !== 'android') return;

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        // Prevent back button from closing the lock screen
        if (isRunning) {
          // Show a gentle reminder instead
          Alert.alert(
            'Prayer Time Active',
            'Use the exit button to leave prayer time early.',
            [{ text: 'OK' }]
          );
          return true; // Prevent default behavior
        }
        return false; // Allow default behavior when timer is not running
      }
    );

    return () => backHandler.remove();
  }, [visible, isRunning]);

  // Monitor app state to detect when user tries to leave
  useEffect(() => {
    if (!visible || !isRunning) return;

    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        console.log('User tried to leave app during prayer time');

        // Record exit attempt
        recordExitAttempt();

        // Show alert when they return
        setTimeout(() => {
          if (isRunning) {
            Alert.alert(
              'Stay Focused',
              'Prayer time is still active. Please complete your prayer session.',
              [{ text: 'OK' }]
            );
          }
        }, 500);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );
    return () => subscription?.remove();
  }, [visible, isRunning, recordExitAttempt]);

  // Initialize overlay service when component mounts
  useEffect(() => {
    if (visible && Platform.OS === 'android') {
      initializeOverlay();
    }

    return () => {
      if (Platform.OS === 'android') {
        AndroidOverlayService.deactivateOverlay();
        AndroidOverlayService.disableKioskMode();
      }
    };
  }, [visible]);

  const initializeOverlay = async () => {
    try {
      const permissionStatus =
        await AndroidOverlayService.checkOverlayPermission();
      setOverlayPermissionGranted(permissionStatus.granted);

      if (permissionStatus.granted) {
        // Activate system overlay and kiosk mode
        await AndroidOverlayService.activateOverlay();
        await AndroidOverlayService.enableKioskMode();
      }
    } catch (error) {
      console.error('Error initializing overlay:', error);
    }
  };

  const handlePermissionGranted = () => {
    // User indicates they've granted permission
    AndroidOverlayService.setPermissionGranted(true);
    setOverlayPermissionGranted(true);

    // Activate overlay features
    AndroidOverlayService.activateOverlay();
    AndroidOverlayService.enableKioskMode();
  };

  const handleLongPressStart = () => {
    console.log('Long press started');

    // Start long press animation
    exitButtonScale.value = withTiming(0.95, { duration: 100 });
    exitButtonOpacity.value = withTiming(1, { duration: 100 });

    // Start progress animation
    progressWidth.value = withTiming(100, { duration: 3000 });

    // Start timer for long press detection
    longPressTimer.current = setTimeout(() => {
      console.log('Long press completed - showing confirmation');

      // Long press completed - show confirmation
      setShowExitConfirmation(true);
      recordExitAttempt();

      // Reset animations after showing confirmation
      exitButtonScale.value = withTiming(1, { duration: 100 });
      exitButtonOpacity.value = withTiming(0.7, { duration: 100 });
      progressWidth.value = withTiming(0, { duration: 200 });
    }, 3000);
  };

  const handleLongPressEnd = () => {
    console.log('Long press ended');

    // Only reset if timer hasn't completed yet
    if (longPressTimer.current) {
      console.log('Resetting long press timer');

      // Reset animations
      exitButtonScale.value = withTiming(1, { duration: 100 });
      exitButtonOpacity.value = withTiming(0.7, { duration: 100 });
      progressWidth.value = withTiming(0, { duration: 200 });

      // Clear timer
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  };

  const handleConfirmExit = async () => {
    console.log('User confirmed exit');
    setShowExitConfirmation(false);
    stopTimer();

    // Deactivate overlay and kiosk mode
    if (Platform.OS === 'android') {
      await AndroidOverlayService.deactivateOverlay();
      await AndroidOverlayService.disableKioskMode();
    }

    onExit?.();
  };

  const handleCancelExit = () => {
    console.log('User cancelled exit');
    setShowExitConfirmation(false);
  };

  const handleEmergencyAccess = () => {
    AndroidOverlayService.showEmergencyAccess();
  };

  const exitButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: exitButtonScale.value }],
    opacity: exitButtonOpacity.value,
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`,
  }));

  const getGuiltMessage = () => {
    const messages = [
      'This is your sacred time with Allah. Are you sure you want to skip it?',
      'Prayer is the pillar of faith. Consider staying for your spiritual well-being.',
      'These 15 minutes could bring you closer to Allah. Please reconsider.',
      'Your soul needs this time more than your phone does.',
      'Remember, prayer is better than sleep, and better than any distraction.',
    ];

    return messages[exitAttempts % messages.length];
  };

  if (!visible || !isRunning) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="fade"
      presentationStyle="fullScreen"
      statusBarTranslucent={false}
      hardwareAccelerated={true}
    >
      <StatusBar
        barStyle="light-content"
        backgroundColor="#1F2937"
        translucent={false}
        hidden={false}
      />

      <View style={styles.container}>
        {/* Background Gradient Effect */}
        <View style={styles.backgroundGradient} />

        {/* System Lock Indicator */}
        {Platform.OS === 'android' && (
          <View style={styles.systemLockIndicator}>
            <Text style={styles.systemLockText}>
              {overlayPermissionGranted
                ? '🔒 System Lock Active'
                : '📱 App Lock Active'}
            </Text>
            {!overlayPermissionGranted && (
              <Pressable
                style={styles.permissionButton}
                onPress={() => {
                  Alert.alert(
                    'Enable System Lock',
                    'For true system lock, enable "Display over other apps" permission in Android settings.',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: "I've enabled it",
                        onPress: handlePermissionGranted,
                      },
                      {
                        text: 'Open Settings',
                        onPress: () =>
                          AndroidOverlayService.requestOverlayPermission(),
                      },
                    ]
                  );
                }}
              >
                <Text style={styles.permissionHint}>
                  Tap to enable system lock
                </Text>
              </Pressable>
            )}
          </View>
        )}

        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.prayerTitle}>
            {currentPrayer || 'Prayer Time'}
          </Text>
          <Text style={styles.subtitle}>
            Focus on your connection with Allah
          </Text>
        </View>

        {/* Timer Display */}
        <View style={styles.timerContainer}>
          <TimerDisplay
            size={250}
            strokeWidth={6}
            color="#10B981"
            backgroundColor="#374151"
            textColor="#F9FAFB"
          />
        </View>

        {/* Spiritual Content Area */}
        <View style={styles.contentContainer}>
          <Text style={styles.spiritualText}>
            "And establish prayer and give zakah and bow with those who bow."
          </Text>
          <Text style={styles.verseReference}>- Quran 2:43</Text>
        </View>

        {/* Control Buttons */}
        <View style={styles.controlContainer}>
          {/* Emergency Access Button */}
          {Platform.OS === 'android' && (
            <Pressable
              style={styles.emergencyButton}
              onPress={handleEmergencyAccess}
            >
              <Text style={styles.emergencyButtonText}>Emergency</Text>
            </Pressable>
          )}

          {/* Exit Button */}
          <Animated.View style={[styles.exitButton, exitButtonAnimatedStyle]}>
            <Pressable
              onPressIn={handleLongPressStart}
              onPressOut={handleLongPressEnd}
              style={styles.exitPressable}
            >
              <Text style={styles.exitButtonText}>Hold to Exit</Text>

              {/* Progress Bar */}
              <View style={styles.progressBar}>
                <Animated.View
                  style={[styles.progressFill, progressAnimatedStyle]}
                />
              </View>

              <Text style={styles.exitHint}>Hold for 3 seconds</Text>
            </Pressable>
          </Animated.View>

          {exitAttempts > 0 && (
            <Text style={styles.attemptWarning}>
              Exit attempts: {exitAttempts}
            </Text>
          )}
        </View>

        {/* Exit Confirmation Overlay */}
        {showExitConfirmation && (
          <View style={styles.confirmationOverlay}>
            <View style={styles.confirmationContainer}>
              <Text style={styles.confirmationTitle}>Skip Prayer Time?</Text>

              <Text style={styles.confirmationMessage}>
                {getGuiltMessage()}
              </Text>

              <View style={styles.confirmationButtons}>
                <Pressable
                  style={[styles.confirmationButton, styles.cancelButton]}
                  onPress={handleCancelExit}
                >
                  <Text style={styles.cancelButtonText}>Stay & Pray</Text>
                </Pressable>

                <Pressable
                  style={[styles.confirmationButton, styles.exitConfirmButton]}
                  onPress={handleConfirmExit}
                >
                  <Text style={styles.exitConfirmButtonText}>Skip</Text>
                </Pressable>
              </View>
            </View>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1F2937',
    justifyContent: 'space-between',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#1F2937',
    opacity: 0.95,
  },
  systemLockIndicator: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    alignItems: 'center',
    zIndex: 10,
  },
  systemLockText: {
    color: '#10B981',
    fontSize: 14,
    fontWeight: '600',
  },
  permissionButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    backgroundColor: 'rgba(245, 158, 11, 0.2)',
    borderWidth: 1,
    borderColor: '#F59E0B',
  },
  permissionHint: {
    color: '#F59E0B',
    fontSize: 12,
    textAlign: 'center',
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
  },
  prayerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#F9FAFB',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#D1D5DB',
    textAlign: 'center',
    marginTop: 8,
  },
  timerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  contentContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 40,
  },
  spiritualText: {
    fontSize: 18,
    color: '#F9FAFB',
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 26,
  },
  verseReference: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    marginTop: 8,
  },
  controlContainer: {
    alignItems: 'center',
    gap: 16,
  },
  emergencyButton: {
    backgroundColor: '#DC2626',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginBottom: 10,
  },
  emergencyButtonText: {
    color: '#F9FAFB',
    fontSize: 14,
    fontWeight: '600',
  },
  exitButton: {
    backgroundColor: '#374151',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#4B5563',
  },
  exitPressable: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    alignItems: 'center',
    minWidth: 160,
  },
  exitButtonText: {
    color: '#F9FAFB',
    fontSize: 16,
    fontWeight: '600',
  },
  progressBar: {
    width: 120,
    height: 3,
    backgroundColor: '#4B5563',
    borderRadius: 2,
    marginTop: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#EF4444',
    borderRadius: 2,
  },
  exitHint: {
    color: '#9CA3AF',
    fontSize: 12,
    marginTop: 4,
  },
  attemptWarning: {
    color: '#F59E0B',
    fontSize: 14,
    marginTop: 12,
    textAlign: 'center',
  },
  confirmationOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  confirmationContainer: {
    backgroundColor: '#374151',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    minWidth: 300,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  confirmationTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#F9FAFB',
    textAlign: 'center',
    marginBottom: 16,
  },
  confirmationMessage: {
    fontSize: 16,
    color: '#D1D5DB',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  confirmationButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  confirmationButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#10B981',
  },
  exitConfirmButton: {
    backgroundColor: '#EF4444',
  },
  cancelButtonText: {
    color: '#F9FAFB',
    fontSize: 16,
    fontWeight: '600',
  },
  exitConfirmButtonText: {
    color: '#F9FAFB',
    fontSize: 16,
    fontWeight: '600',
  },
});
