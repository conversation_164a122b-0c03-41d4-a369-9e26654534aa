{"expo": {"name": "TaqwaApp", "slug": "TaqwaApp", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "taqwa<PERSON><PERSON>", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.talha_sde.TaqwaApp", "permissions": ["SYSTEM_ALERT_WINDOW", "WAKE_LOCK", "FOREGROUND_SERVICE", "RECEIVE_BOOT_COMPLETED", "VIBRATE", "POST_NOTIFICATIONS", "SCHEDULE_EXACT_ALARM", "USE_EXACT_ALARM"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "./plugins/android-system-lock", ["expo-notifications", {"icon": "./assets/images/icon.png", "color": "#4F46E5"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "4fb6968a-0e70-48a9-8021-62946c5ae27c"}}}}