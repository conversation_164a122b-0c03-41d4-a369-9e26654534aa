import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Modal,
  StatusBar,
  BackHandler,
  Platform,
  Alert,
  AppState,
  Dimensions,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import {
  useTimerStore,
  useTimerStatus,
  usePrayerContext,
} from '../store/timerStore';
import { TimerDisplay } from './TimerDisplay';
import { useTimer } from '../hooks/useTimer';
import { launcherService } from '../services/LauncherService';

interface LauncherLockScreenProps {
  visible: boolean;
  onExit?: () => void;
}

export const LauncherLockScreen: React.FC<LauncherLockScreenProps> = ({
  visible,
  onExit,
}) => {
  const [showExitConfirmation, setShowExitConfirmation] = useState(false);
  const [launcherModeActive, setLauncherModeActive] = useState(false);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);

  const { 
    stopTimer, 
    recordExitAttempt, 
    deactivateLauncherMode,
    isLauncherMode,
    launcherModeSupported 
  } = useTimerStore();
  const { isRunning } = useTimerStatus();
  const { currentPrayer, exitAttempts } = usePrayerContext();

  // Initialize timer hook
  useTimer();

  // Animation values
  const exitButtonScale = useSharedValue(1);
  const exitButtonOpacity = useSharedValue(0.7);
  const progressWidth = useSharedValue(0);

  // Check launcher mode status
  useEffect(() => {
    const checkLauncherStatus = async () => {
      if (Platform.OS === 'android' && visible) {
        const isLauncher = await launcherService.isDefaultLauncher();
        setLauncherModeActive(isLauncher);
      }
    };

    checkLauncherStatus();
    
    // Check periodically while visible
    const interval = setInterval(checkLauncherStatus, 2000);
    
    return () => clearInterval(interval);
  }, [visible]);

  // Handle Android back button
  useEffect(() => {
    if (!visible || Platform.OS !== 'android') return;

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        // Prevent back button from closing the lock screen
        if (isRunning) {
          // Show a gentle reminder instead
          Alert.alert(
            'Prayer Time Active',
            'Use the exit button to leave prayer time early.',
            [{ text: 'OK' }]
          );
          return true; // Prevent default behavior
        }
        return false; // Allow default behavior when timer is not running
      }
    );

    return () => backHandler.remove();
  }, [visible, isRunning]);

  // Monitor app state to detect when user tries to leave
  useEffect(() => {
    if (!visible || !isRunning) return;

    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        console.log('User tried to leave app during prayer time');

        // Record exit attempt
        recordExitAttempt();

        // Show alert when they return (only if not in launcher mode)
        if (!launcherModeActive) {
          setTimeout(() => {
            if (isRunning) {
              Alert.alert(
                'Stay Focused',
                'Prayer time is still active. Please complete your prayer session.',
                [{ text: 'OK' }]
              );
            }
          }, 500);
        }
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );
    return () => subscription?.remove();
  }, [visible, isRunning, recordExitAttempt, launcherModeActive]);

  const handleLongPressStart = () => {
    console.log('Long press started');

    // Start long press animation
    exitButtonScale.value = withTiming(0.95, { duration: 100 });
    exitButtonOpacity.value = withTiming(1, { duration: 100 });

    // Start progress animation
    progressWidth.value = withTiming(100, { duration: 3000 });

    // Start timer for long press detection
    longPressTimer.current = setTimeout(() => {
      console.log('Long press completed - showing confirmation');

      // Long press completed - show confirmation
      setShowExitConfirmation(true);
      recordExitAttempt();

      // Reset animations after showing confirmation
      exitButtonScale.value = withTiming(1, { duration: 100 });
      exitButtonOpacity.value = withTiming(0.7, { duration: 100 });
      progressWidth.value = withTiming(0, { duration: 200 });
    }, 3000);
  };

  const handleLongPressEnd = () => {
    console.log('Long press ended');

    // Clear timer if still running
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }

    // Reset animations
    exitButtonScale.value = withTiming(1, { duration: 100 });
    exitButtonOpacity.value = withTiming(0.7, { duration: 100 });
    progressWidth.value = withTiming(0, { duration: 200 });
  };

  const handleConfirmExit = async () => {
    console.log('User confirmed exit');
    setShowExitConfirmation(false);
    stopTimer();

    // Deactivate launcher mode if active
    if (launcherModeActive && Platform.OS === 'android') {
      await deactivateLauncherMode();
    }

    onExit?.();
  };

  const handleCancelExit = () => {
    console.log('User cancelled exit');
    setShowExitConfirmation(false);
  };

  const handleLauncherSettings = async () => {
    try {
      await launcherService.openLauncherSettings();
    } catch (error) {
      Alert.alert(
        'Settings Error',
        'Could not open launcher settings. Please go to Settings > Apps > Default Apps > Home app manually.',
        [{ text: 'OK' }]
      );
    }
  };

  const exitButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: exitButtonScale.value }],
    opacity: exitButtonOpacity.value,
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`,
  }));

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="fade"
      presentationStyle="fullScreen"
      statusBarTranslucent
    >
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent
      />
      
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Prayer Time</Text>
          <Text style={styles.subtitle}>{currentPrayer}</Text>
          
          {/* Launcher Mode Indicator */}
          {Platform.OS === 'android' && (
            <View style={styles.launcherIndicator}>
              <Text style={styles.launcherText}>
                {launcherModeActive 
                  ? '🔒 Launcher Lock Active' 
                  : launcherModeSupported 
                    ? '⚠️ Soft Lock Mode' 
                    : '⚠️ Limited Lock Support'
                }
              </Text>
              {!launcherModeActive && launcherModeSupported && (
                <Pressable 
                  style={styles.launcherButton}
                  onPress={handleLauncherSettings}
                >
                  <Text style={styles.launcherButtonText}>
                    Enable Launcher Lock
                  </Text>
                </Pressable>
              )}
            </View>
          )}
        </View>

        {/* Timer Display */}
        <View style={styles.timerContainer}>
          <TimerDisplay />
        </View>

        {/* Exit Attempts Counter */}
        {exitAttempts > 0 && (
          <View style={styles.exitAttemptsContainer}>
            <Text style={styles.exitAttemptsText}>
              Exit attempts: {exitAttempts}
            </Text>
            <Text style={styles.exitAttemptsHint}>
              Stay focused on your prayer
            </Text>
          </View>
        )}

        {/* Control Buttons */}
        <View style={styles.controlContainer}>
          {/* Exit Button */}
          <Animated.View style={[styles.exitButton, exitButtonAnimatedStyle]}>
            <Pressable
              onPressIn={handleLongPressStart}
              onPressOut={handleLongPressEnd}
              style={styles.exitPressable}
            >
              <Text style={styles.exitButtonText}>Hold to Exit</Text>

              {/* Progress Bar */}
              <View style={styles.progressBar}>
                <Animated.View
                  style={[styles.progressFill, progressAnimatedStyle]}
                />
              </View>

              <Text style={styles.exitHint}>Hold for 3 seconds</Text>
            </Pressable>
          </Animated.View>
        </View>

        {/* Exit Confirmation Modal */}
        <Modal
          visible={showExitConfirmation}
          transparent
          animationType="fade"
        >
          <View style={styles.confirmationOverlay}>
            <View style={styles.confirmationContainer}>
              <Text style={styles.confirmationTitle}>Exit Prayer Time?</Text>
              <Text style={styles.confirmationMessage}>
                Are you sure you want to end your prayer session early?
              </Text>
              
              <View style={styles.confirmationButtons}>
                <Pressable
                  style={[styles.confirmationButton, styles.cancelButton]}
                  onPress={handleCancelExit}
                >
                  <Text style={styles.cancelButtonText}>Continue Prayer</Text>
                </Pressable>
                
                <Pressable
                  style={[styles.confirmationButton, styles.exitConfirmButton]}
                  onPress={handleConfirmExit}
                >
                  <Text style={styles.exitConfirmButtonText}>Exit</Text>
                </Pressable>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
};
