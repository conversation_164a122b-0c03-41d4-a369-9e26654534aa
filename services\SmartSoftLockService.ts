import { AppState, AppStateStatus, Alert, Platform } from 'react-native';
import * as Notifications from 'expo-notifications';

export interface SoftLockState {
  isActive: boolean;
  exitAttempts: number;
  lastExitTime: Date | null;
  notificationId: string | null;
}

class SmartSoftLockService {
  private isActive = false;
  private exitAttempts = 0;
  private lastExitTime: Date | null = null;
  private notificationId: string | null = null;
  private appStateSubscription: any = null;
  private persistentTimer: any = null;
  private onReturnCallback: (() => void) | null = null;

  /**
   * Activate smart soft lock during prayer
   */
  async activateSoftLock(onReturn?: () => void): Promise<void> {
    console.log('🔒 Activating Smart Soft Lock');

    this.isActive = true;
    this.exitAttempts = 0;
    this.lastExitTime = null;
    this.onReturnCallback = onReturn || null;

    // Initialize notifications first
    await this.initializeNotifications();

    // Set up app state monitoring
    this.setupAppStateMonitoring();

    // Start persistent notification system
    this.startPersistentNotifications();

    console.log('✅ Smart Soft Lock activated');
  }

  /**
   * Initialize notification system
   */
  private async initializeNotifications(): Promise<void> {
    try {
      // Request permissions
      const { status } = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowAnnouncements: true,
        },
      });

      if (status !== 'granted') {
        console.warn('⚠️ Notification permissions not granted');
        return;
      }

      // Configure notification behavior
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowBanner: true,
          shouldShowList: true,
          shouldPlaySound: true,
          shouldSetBadge: false,
          priority: Notifications.AndroidNotificationPriority.MAX,
        }),
      });

      // Configure Android notification channel
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('prayer-lock', {
          name: 'Prayer Lock',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#4F46E5',
          sound: true,
          enableVibrate: true,
          showBadge: false,
          lockscreenVisibility:
            Notifications.AndroidNotificationVisibility.PUBLIC,
        });
      }

      console.log('✅ Notifications initialized');
    } catch (error) {
      console.error('❌ Failed to initialize notifications:', error);
    }
  }

  /**
   * Deactivate soft lock
   */
  async deactivateSoftLock(): Promise<void> {
    console.log('🔓 Deactivating Smart Soft Lock');

    this.isActive = false;
    this.exitAttempts = 0;
    this.lastExitTime = null;
    this.onReturnCallback = null;

    // Clean up monitoring
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }

    // Clear persistent timer
    if (this.persistentTimer) {
      clearInterval(this.persistentTimer);
      this.persistentTimer = null;
    }

    // Dismiss notifications
    await this.dismissAllNotifications();

    console.log('Smart Soft Lock deactivated');
  }

  /**
   * Set up app state monitoring
   */
  private setupAppStateMonitoring(): void {
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange.bind(this)
    );
  }

  /**
   * Handle app state changes
   */
  private handleAppStateChange(nextAppState: AppStateStatus): void {
    if (!this.isActive) return;

    if (nextAppState === 'background' || nextAppState === 'inactive') {
      // User tried to leave during prayer
      this.exitAttempts++;
      this.lastExitTime = new Date();

      console.log(`🚨 Exit attempt #${this.exitAttempts} during prayer`);

      // Try to bring app back to foreground immediately
      this.attemptAppReopen();

      // Also show notification as backup
      this.showReturnNotification();
    } else if (nextAppState === 'active') {
      // User returned to app
      if (this.exitAttempts > 0) {
        console.log('📱 User returned to app during prayer');

        // Dismiss notification
        this.dismissAllNotifications();

        // Show guilt prompt based on attempts
        this.showReturnPrompt();

        // Trigger callback
        if (this.onReturnCallback) {
          this.onReturnCallback();
        }
      }
    }
  }

  /**
   * Start persistent notification system
   */
  private startPersistentNotifications(): void {
    // Check every 2 seconds if user is away during prayer
    this.persistentTimer = setInterval(() => {
      if (this.isActive && this.lastExitTime) {
        const timeSinceExit = Date.now() - this.lastExitTime.getTime();

        // If user has been away for more than 2 seconds, show urgent notification
        if (timeSinceExit > 2000 && timeSinceExit < 5000) {
          this.attemptAppReopen();
        }

        // If user has been away for more than 5 seconds, show persistent notification
        if (timeSinceExit > 5000) {
          this.showPersistentReturnNotification();
        }
      }
    }, 2000); // Check every 2 seconds instead of 5
  }

  /**
   * Attempt to reopen the app using notifications and user guidance
   */
  private async attemptAppReopen(): Promise<void> {
    try {
      console.log('🔄 Attempting to guide user back to app');

      // Show immediate high-priority notification
      await this.showUrgentReturnNotification();

      // For now, we rely on notifications to guide user back
      // In a production app, you could implement native foreground methods
      console.log('📱 Urgent notification sent to guide user back');
    } catch (error) {
      console.error('❌ App reopen guidance failed:', error);
    }
  }

  /**
   * Show urgent notification to bring user back immediately
   */
  private async showUrgentReturnNotification(): Promise<void> {
    try {
      // Dismiss any existing notifications first
      await this.dismissAllNotifications();

      this.notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '🚨 Return to Prayer NOW',
          body: 'Your prayer session is active. Tap to return immediately.',
          sound: true,
          priority: Notifications.AndroidNotificationPriority.MAX,
          sticky: true,
          autoDismiss: false,
          vibrate: [0, 500, 200, 500, 200, 500],
          ...(Platform.OS === 'android' && {
            channelId: 'prayer-lock',
            color: '#FF0000', // Red for urgency
          }),
        },
        trigger: null,
      });

      console.log('🚨 Urgent return notification sent');
    } catch (error) {
      console.error('❌ Failed to show urgent notification:', error);
    }
  }

  /**
   * Show immediate return notification
   */
  private async showReturnNotification(): Promise<void> {
    try {
      // First ensure we have notification permissions
      const { status } = await Notifications.getPermissionsAsync();
      if (status !== 'granted') {
        console.log('Requesting notification permissions...');
        const { status: newStatus } =
          await Notifications.requestPermissionsAsync();
        if (newStatus !== 'granted') {
          console.error('Notification permissions not granted');
          return;
        }
      }

      const messages = [
        '🕌 Prayer time is still active',
        '📿 Please return to complete your prayer',
        '🤲 Allah is waiting for your prayer',
        '⏰ Your prayer session needs completion',
      ];

      const message =
        messages[Math.min(this.exitAttempts - 1, messages.length - 1)];

      this.notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Return to Prayer',
          body: message,
          sound: true,
          priority: Notifications.AndroidNotificationPriority.MAX,
          sticky: true,
          autoDismiss: false,
          vibrate: [0, 300, 200, 300],
          ...(Platform.OS === 'android' && {
            channelId: 'prayer-lock',
            color: '#4F46E5',
          }),
        },
        trigger: null,
      });

      console.log('📱 Return notification sent with ID:', this.notificationId);
    } catch (error) {
      console.error('❌ Failed to show return notification:', error);
    }
  }

  /**
   * Show persistent return notification for long absences
   */
  private async showPersistentReturnNotification(): Promise<void> {
    try {
      await this.dismissAllNotifications();

      const secondsAway = Math.floor(
        (Date.now() - this.lastExitTime!.getTime()) / 1000
      );

      this.notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '🕌 PRAYER ACTIVE - RETURN NOW',
          body: `Away for ${secondsAway}s. Allah is waiting. Tap to return to prayer.`,
          sound: true,
          priority: Notifications.AndroidNotificationPriority.MAX,
          sticky: true,
          autoDismiss: false,
          vibrate: [0, 400, 100, 400, 100, 400, 100, 400],
          ...(Platform.OS === 'android' && {
            channelId: 'prayer-lock',
            color: '#FF4444', // Bright red for maximum attention
          }),
        },
        trigger: null,
      });

      console.log(`📢 Persistent notification sent (away for ${secondsAway}s)`);
    } catch (error) {
      console.error('Failed to show persistent notification:', error);
    }
  }

  /**
   * Show return prompt when user comes back
   */
  private showReturnPrompt(): void {
    const messages = [
      'Welcome back. Please focus on your prayer.',
      "You left during prayer time. Let's continue with devotion.",
      'Multiple exits detected. Remember, Allah sees everything.',
      'Frequent interruptions weaken our connection with Allah.',
    ];

    const title = this.exitAttempts === 1 ? 'Welcome Back' : 'Focus on Prayer';
    const message =
      messages[Math.min(this.exitAttempts - 1, messages.length - 1)];

    Alert.alert(title, message, [
      {
        text: 'Continue Prayer',
        style: 'default',
      },
    ]);
  }

  /**
   * Dismiss all notifications
   */
  private async dismissAllNotifications(): Promise<void> {
    try {
      if (this.notificationId) {
        await Notifications.dismissNotificationAsync(this.notificationId);
        this.notificationId = null;
      }
      await Notifications.dismissAllNotificationsAsync();
    } catch (error) {
      console.error('Failed to dismiss notifications:', error);
    }
  }

  /**
   * Get current soft lock state
   */
  getState(): SoftLockState {
    return {
      isActive: this.isActive,
      exitAttempts: this.exitAttempts,
      lastExitTime: this.lastExitTime,
      notificationId: this.notificationId,
    };
  }

  /**
   * Force emergency exit (for testing)
   */
  async emergencyExit(): Promise<void> {
    console.log('🚨 Emergency exit triggered');
    await this.deactivateSoftLock();
  }
}

export const smartSoftLockService = new SmartSoftLockService();
