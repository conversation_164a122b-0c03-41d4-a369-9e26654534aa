import { MMKV } from 'react-native-mmkv';

// Initialize MMKV storage
const storage = new MMKV();

export { storage };

// Helper functions for common storage operations
export const StorageService = {
  // String operations
  setString: (key: string, value: string): void => {
    storage.set(key, value);
  },

  getString: (key: string): string | undefined => {
    return storage.getString(key);
  },

  // Object operations (JSON)
  setObject: (key: string, value: any): void => {
    storage.set(key, JSON.stringify(value));
  },

  getObject: <T = any>(key: string): T | null => {
    const value = storage.getString(key);
    if (value) {
      try {
        return JSON.parse(value) as T;
      } catch (error) {
        console.error('Failed to parse stored object:', error);
        return null;
      }
    }
    return null;
  },

  // Boolean operations
  setBoolean: (key: string, value: boolean): void => {
    storage.set(key, value);
  },

  getBoolean: (key: string): boolean | undefined => {
    return storage.getBoolean(key);
  },

  // Number operations
  setNumber: (key: string, value: number): void => {
    storage.set(key, value);
  },

  getNumber: (key: string): number | undefined => {
    return storage.getNumber(key);
  },

  // Generic operations
  remove: (key: string): void => {
    storage.delete(key);
  },

  clear: (): void => {
    storage.clearAll();
  },

  contains: (key: string): boolean => {
    return storage.contains(key);
  },

  getAllKeys: (): string[] => {
    return storage.getAllKeys();
  },
};
