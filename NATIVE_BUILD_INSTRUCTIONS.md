# Native Android System Lock - Build Instructions

## 🚀 Overview

We've implemented a **native Android module** that provides true system-level lock capabilities:

- **Real overlay permission checking**
- **True kiosk mode** (immersive fullscreen)
- **System UI hiding** (status bar, navigation bar)
- **Task switcher prevention**
- **Screen security** (prevents screenshots)

## 📋 Prerequisites

1. **Android Studio** installed
2. **Android SDK** configured
3. **Java 11+** installed
4. **Expo CLI** updated to latest version

## 🔧 Build Steps

### Step 1: Install EAS CLI
```bash
npm install -g @expo/eas-cli
```

### Step 2: Configure EAS Build
```bash
cd TaqwaApp
eas build:configure
```

### Step 3: Create Development Build
```bash
# For Android device/emulator
eas build --platform android --profile development

# Or for local build (if you have Android Studio setup)
eas build --platform android --profile development --local
```

### Step 4: Install Development Build
1. Download the APK from EAS build
2. Install on your Android device
3. Install Expo Dev Client if not included

### Step 5: Start Development Server
```bash
npx expo start --dev-client
```

## 🎯 Native Features

### System Lock Capabilities
- **Immersive Mode**: Hides status bar and navigation bar
- **Prevent Screenshots**: Adds FLAG_SECURE to window
- **Keep Screen On**: Prevents screen from turning off
- **Task Switcher Block**: Attempts to prevent app switching

### Permissions Required
- `SYSTEM_ALERT_WINDOW` - For overlay windows
- `WAKE_LOCK` - To keep screen on
- `DISABLE_KEYGUARD` - To disable lock screen
- `REORDER_TASKS` - To manage task stack

## 🧪 Testing the Native Lock

### Test Sequence:
1. **Start Prayer Timer** → **Test Lock Screen**
2. **Tap "I've enabled it"** for overlay permission
3. **Try system gestures**:
   - Swipe up from bottom (should be blocked)
   - Swipe down from top (should be blocked)
   - Recent apps button (should be blocked)
   - Home button (should be blocked)

### Expected Behavior:
- **Status bar hidden**
- **Navigation bar hidden**
- **Immersive fullscreen mode**
- **Cannot access system UI**
- **Cannot switch apps**

## 🔧 Troubleshooting

### Build Issues:
```bash
# Clear cache
npx expo install --fix

# Clean and rebuild
eas build --platform android --profile development --clear-cache
```

### Permission Issues:
1. Go to **Settings > Apps > Taqwa App > Advanced**
2. Enable **"Display over other apps"**
3. Return to app and tap **"I've enabled it"**

### Module Not Found:
```bash
# Reinstall dependencies
npm install
npx expo install --fix
```

## 📱 Alternative: Launcher Approach

If the native module approach has issues, we can implement a **launcher-based solution**:

1. **Make app a launcher** during prayer time
2. **Replace home screen** temporarily
3. **Intercept all system intents**
4. **Restore original launcher** after prayer

This would provide **100% system lock** but requires more complex implementation.

## 🎯 Current Status

- ✅ **Native module created**
- ✅ **Config plugin configured**
- ✅ **TypeScript interfaces ready**
- ✅ **Service integration complete**
- 🔄 **Requires development build to test**

## 🚀 Next Steps

1. **Build development APK** with EAS
2. **Test on real Android device**
3. **Verify system lock effectiveness**
4. **Iterate based on results**

The native approach should provide **true system-level lock** that prevents users from leaving the app during prayer time! 🕌
