import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { useTimerProgress } from '../store/timerStore';
import { useFormattedTime } from '../hooks/useTimer';

interface TimerDisplayProps {
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  textColor?: string;
}

export const TimerDisplay: React.FC<TimerDisplayProps> = ({
  size = 200,
  strokeWidth = 8,
  color = '#4F46E5',
  backgroundColor = '#E5E7EB',
  textColor = '#1F2937',
}) => {
  const { progress, percentage } = useTimerProgress();
  const { formatted, minutes, seconds } = useFormattedTime();

  const animatedProgress = useSharedValue(0);

  React.useEffect(() => {
    animatedProgress.value = withTiming(progress, { duration: 1000 });
  }, [progress, animatedProgress]);

  const radius = (size - strokeWidth) / 2;

  const animatedStyle = useAnimatedStyle(() => {
    const rotation = interpolate(
      animatedProgress.value,
      [0, 1],
      [0, 360],
      Extrapolate.CLAMP
    );

    return {
      transform: [{ rotate: `${rotation}deg` }],
    };
  });

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      {/* Background Circle */}
      <View
        style={[
          styles.circle,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            borderWidth: strokeWidth,
            borderColor: backgroundColor,
          },
        ]}
      />

      {/* Progress Circle */}
      <Animated.View
        style={[
          styles.progressCircle,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            borderWidth: strokeWidth,
            borderColor: color,
          },
          animatedStyle,
        ]}
      />

      {/* Timer Text */}
      <View style={styles.textContainer}>
        <Text style={[styles.timeText, { color: textColor }]}>{formatted}</Text>
        <Text style={[styles.percentageText, { color: textColor }]}>
          {percentage}% Complete
        </Text>
        <View style={styles.detailContainer}>
          <Text style={[styles.detailText, { color: textColor }]}>
            {minutes}m {seconds}s remaining
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  circle: {
    position: 'absolute',
    backgroundColor: 'transparent',
  },
  progressCircle: {
    position: 'absolute',
    backgroundColor: 'transparent',
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
    transform: [{ rotate: '-90deg' }],
  },
  textContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
  },
  timeText: {
    fontSize: 32,
    fontWeight: 'bold',
    fontFamily: 'monospace',
  },
  percentageText: {
    fontSize: 14,
    marginTop: 4,
    opacity: 0.7,
  },
  detailContainer: {
    marginTop: 8,
  },
  detailText: {
    fontSize: 12,
    opacity: 0.6,
  },
});

// Alternative simple text-only timer display
export const SimpleTimerDisplay: React.FC<{
  textColor?: string;
  fontSize?: number;
}> = ({ textColor = '#1F2937', fontSize = 48 }) => {
  const { formatted } = useFormattedTime();

  return (
    <View style={simpleStyles.simpleContainer}>
      <Text
        style={[simpleStyles.simpleTimeText, { color: textColor, fontSize }]}
      >
        {formatted}
      </Text>
    </View>
  );
};

const simpleStyles = StyleSheet.create({
  simpleContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  simpleTimeText: {
    fontWeight: 'bold',
    fontFamily: 'monospace',
  },
});

// Compact timer display for status bars
export const CompactTimerDisplay: React.FC<{
  textColor?: string;
}> = ({ textColor = '#1F2937' }) => {
  const { formatted } = useFormattedTime();
  const { percentage } = useTimerProgress();

  return (
    <View style={compactStyles.container}>
      <View style={compactStyles.progressBar}>
        <Animated.View
          style={[compactStyles.progressFill, { width: `${percentage}%` }]}
        />
      </View>
      <Text style={[compactStyles.timeText, { color: textColor }]}>
        {formatted}
      </Text>
    </View>
  );
};

const compactStyles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  progressBar: {
    width: 60,
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginRight: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4F46E5',
    borderRadius: 2,
  },
  timeText: {
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'monospace',
  },
});
