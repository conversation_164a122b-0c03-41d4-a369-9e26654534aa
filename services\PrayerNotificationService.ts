import * as Notifications from 'expo-notifications';
import * as TaskManager from 'expo-task-manager';
import * as BackgroundFetch from 'expo-background-fetch';
import { Platform } from 'react-native';
import { prayerTimeService } from './PrayerTimeService';
import { useTimerStore } from '../store/timerStore';

const PRAYER_CHECK_TASK = 'prayer-time-check';
const NOTIFICATION_CHANNEL_ID = 'prayer-notifications';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

interface PrayerNotificationSettings {
  enableNotifications: boolean;
  notifyBeforePrayer: boolean;
  minutesBeforeNotification: number;
  autoStartLock: boolean;
  enableAdhan: boolean;
}

class PrayerNotificationService {
  private static instance: PrayerNotificationService;
  private isInitialized = false;
  private settings: PrayerNotificationSettings = {
    enableNotifications: true,
    notifyBeforePrayer: true,
    minutesBeforeNotification: 5,
    autoStartLock: false,
    enableAdhan: true,
  };
  private scheduledNotifications: string[] = [];

  static getInstance(): PrayerNotificationService {
    if (!PrayerNotificationService.instance) {
      PrayerNotificationService.instance = new PrayerNotificationService();
    }
    return PrayerNotificationService.instance;
  }

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Request notification permissions
      const { status } = await Notifications.requestPermissionsAsync();
      if (status !== 'granted') {
        console.log('Notification permissions not granted');
        return;
      }

      // Create notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync(NOTIFICATION_CHANNEL_ID, {
          name: 'Prayer Notifications',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#4F46E5',
          sound: 'default',
        });
      }

      // Register background task
      await this.registerBackgroundTask();

      // Schedule initial notifications
      await this.scheduleAllPrayerNotifications();

      this.isInitialized = true;
      console.log('PrayerNotificationService initialized');
    } catch (error) {
      console.error('Failed to initialize PrayerNotificationService:', error);
    }
  }

  /**
   * Register background task for prayer time checking
   */
  private async registerBackgroundTask(): Promise<void> {
    try {
      // Define the background task
      TaskManager.defineTask(PRAYER_CHECK_TASK, async () => {
        try {
          console.log('Background task: Checking prayer times');
          
          // Check if it's currently prayer time
          const { isPrayerTime, prayerName } = prayerTimeService.isCurrentlyPrayerTime();
          
          if (isPrayerTime && prayerName) {
            // Send immediate prayer notification
            await this.sendPrayerTimeNotification(prayerName);
            
            // Auto-start lock if enabled
            if (this.settings.autoStartLock) {
              const { startTimer, activateLock } = useTimerStore.getState();
              startTimer(`${prayerName} Prayer`);
              activateLock();
            }
          }

          return BackgroundFetch.BackgroundFetchResult.NewData;
        } catch (error) {
          console.error('Background task error:', error);
          return BackgroundFetch.BackgroundFetchResult.Failed;
        }
      });

      // Register the background fetch task
      await BackgroundFetch.registerTaskAsync(PRAYER_CHECK_TASK, {
        minimumInterval: 60, // Check every minute
        stopOnTerminate: false,
        startOnBoot: true,
      });

      console.log('Background task registered');
    } catch (error) {
      console.error('Failed to register background task:', error);
    }
  }

  /**
   * Schedule notifications for all prayer times
   */
  async scheduleAllPrayerNotifications(): Promise<void> {
    try {
      // Cancel existing notifications
      await this.cancelAllScheduledNotifications();

      if (!this.settings.enableNotifications) {
        return;
      }

      const prayerTimes = prayerTimeService.getCurrentPrayerTimes();
      if (!prayerTimes) {
        console.log('No prayer times available for scheduling notifications');
        return;
      }

      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Schedule for today and tomorrow
      for (const date of [today, tomorrow]) {
        await this.schedulePrayerNotificationsForDate(prayerTimes, date);
      }

      console.log('All prayer notifications scheduled');
    } catch (error) {
      console.error('Failed to schedule prayer notifications:', error);
    }
  }

  /**
   * Schedule notifications for a specific date
   */
  private async schedulePrayerNotificationsForDate(prayerTimes: any, date: Date): Promise<void> {
    const prayers = [
      { name: 'Fajr', time: prayerTimes.fajr },
      { name: 'Dhuhr', time: prayerTimes.dhuhr },
      { name: 'Asr', time: prayerTimes.asr },
      { name: 'Maghrib', time: prayerTimes.maghrib },
      { name: 'Isha', time: prayerTimes.isha },
    ];

    for (const prayer of prayers) {
      // Schedule main prayer notification
      await this.schedulePrayerNotification(prayer.name, prayer.time, date);

      // Schedule reminder notification if enabled
      if (this.settings.notifyBeforePrayer) {
        await this.scheduleReminderNotification(prayer.name, prayer.time, date);
      }
    }
  }

  /**
   * Schedule a single prayer notification
   */
  private async schedulePrayerNotification(prayerName: string, prayerTime: string, date: Date): Promise<void> {
    try {
      const [hours, minutes] = prayerTime.split(':').map(Number);
      const notificationDate = new Date(date);
      notificationDate.setHours(hours, minutes, 0, 0);

      // Don't schedule if time has already passed
      if (notificationDate <= new Date()) {
        return;
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: `🕌 ${prayerName} Prayer Time`,
          body: `It's time for ${prayerName} prayer. May Allah accept your worship.`,
          sound: this.settings.enableAdhan ? 'adhan.mp3' : 'default',
          priority: Notifications.AndroidNotificationPriority.MAX,
          vibrate: [0, 300, 200, 300],
          data: {
            type: 'prayer_time',
            prayerName,
            autoLock: this.settings.autoStartLock,
          },
        },
        trigger: {
          date: notificationDate,
        },
      });

      this.scheduledNotifications.push(notificationId);
      console.log(`Scheduled ${prayerName} notification for ${notificationDate.toLocaleString()}`);
    } catch (error) {
      console.error(`Failed to schedule ${prayerName} notification:`, error);
    }
  }

  /**
   * Schedule a reminder notification before prayer
   */
  private async scheduleReminderNotification(prayerName: string, prayerTime: string, date: Date): Promise<void> {
    try {
      const [hours, minutes] = prayerTime.split(':').map(Number);
      const notificationDate = new Date(date);
      notificationDate.setHours(hours, minutes - this.settings.minutesBeforeNotification, 0, 0);

      // Don't schedule if time has already passed
      if (notificationDate <= new Date()) {
        return;
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: `⏰ ${prayerName} Prayer Reminder`,
          body: `${prayerName} prayer is in ${this.settings.minutesBeforeNotification} minutes. Prepare for prayer.`,
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.DEFAULT,
          data: {
            type: 'prayer_reminder',
            prayerName,
            minutesUntil: this.settings.minutesBeforeNotification,
          },
        },
        trigger: {
          date: notificationDate,
        },
      });

      this.scheduledNotifications.push(notificationId);
      console.log(`Scheduled ${prayerName} reminder for ${notificationDate.toLocaleString()}`);
    } catch (error) {
      console.error(`Failed to schedule ${prayerName} reminder:`, error);
    }
  }

  /**
   * Send immediate prayer time notification
   */
  async sendPrayerTimeNotification(prayerName: string): Promise<void> {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: `🕌 ${prayerName} Prayer Time`,
          body: `It's time for ${prayerName} prayer. Click to start prayer mode.`,
          sound: this.settings.enableAdhan ? 'adhan.mp3' : 'default',
          priority: Notifications.AndroidNotificationPriority.MAX,
          vibrate: [0, 300, 200, 300],
          sticky: true,
          data: {
            type: 'prayer_time_immediate',
            prayerName,
            autoLock: this.settings.autoStartLock,
          },
        },
        trigger: null, // Send immediately
      });

      console.log(`Sent immediate ${prayerName} prayer notification`);
    } catch (error) {
      console.error('Failed to send immediate prayer notification:', error);
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllScheduledNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      this.scheduledNotifications = [];
      console.log('All scheduled notifications cancelled');
    } catch (error) {
      console.error('Failed to cancel notifications:', error);
    }
  }

  /**
   * Update notification settings
   */
  async updateSettings(newSettings: Partial<PrayerNotificationSettings>): Promise<void> {
    this.settings = { ...this.settings, ...newSettings };
    
    // Reschedule notifications with new settings
    await this.scheduleAllPrayerNotifications();
    
    console.log('Notification settings updated:', this.settings);
  }

  /**
   * Get current settings
   */
  getSettings(): PrayerNotificationSettings {
    return { ...this.settings };
  }

  /**
   * Handle notification response (when user taps notification)
   */
  handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const { data } = response.notification.request.content;
    
    if (data.type === 'prayer_time' || data.type === 'prayer_time_immediate') {
      if (data.autoLock) {
        // Auto-start prayer lock
        const { startTimer, activateLock } = useTimerStore.getState();
        startTimer(`${data.prayerName} Prayer`);
        activateLock();
      }
    }
  }

  /**
   * Check and handle missed prayers
   */
  async checkMissedPrayers(): Promise<void> {
    // Implementation for tracking missed prayers
    // This could be used to show guilt messages or update statistics
  }
}

export const prayerNotificationService = PrayerNotificationService.getInstance();
