import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { useTimerStore } from '../store/timerStore';

interface StreakDisplayProps {
  showInLockScreen?: boolean;
}

export const StreakDisplay: React.FC<StreakDisplayProps> = ({
  showInLockScreen = false,
}) => {
  const {
    currentStreak,
    longestStreak,
    totalPrayersCompleted,
    totalPrayersSkipped,
  } = useTimerStore();

  // Animation values
  const streakScale = useSharedValue(1);
  const streakOpacity = useSharedValue(1);

  // Animate when streak changes
  React.useEffect(() => {
    if (currentStreak > 0) {
      streakScale.value = withSequence(
        withSpring(1.2, { damping: 8 }),
        withSpring(1, { damping: 8 })
      );
    }
  }, [currentStreak]);

  const streakAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: streakScale.value }],
    opacity: streakOpacity.value,
  }));

  const getStreakEmoji = (streak: number): string => {
    if (streak === 0) return '🤲';
    if (streak < 3) return '🌱';
    if (streak < 7) return '🌿';
    if (streak < 14) return '🌳';
    if (streak < 30) return '⭐';
    return '🏆';
  };

  const getStreakMessage = (streak: number): string => {
    if (streak === 0) return 'Start your prayer journey';
    if (streak === 1) return 'Great start! Keep going';
    if (streak < 3) return 'Building momentum';
    if (streak < 7) return 'Excellent progress!';
    if (streak < 14) return 'Amazing dedication!';
    if (streak < 30) return 'Mashallah! Outstanding!';
    return 'Subhanallah! Incredible streak!';
  };

  const completionRate = totalPrayersCompleted + totalPrayersSkipped > 0
    ? Math.round((totalPrayersCompleted / (totalPrayersCompleted + totalPrayersSkipped)) * 100)
    : 0;

  if (showInLockScreen) {
    return (
      <View style={styles.lockScreenContainer}>
        <Animated.View style={[styles.streakBadge, streakAnimatedStyle]}>
          <Text style={styles.streakEmoji}>{getStreakEmoji(currentStreak)}</Text>
          <Text style={styles.streakNumber}>{currentStreak}</Text>
        </Animated.View>
        <Text style={styles.streakMessage}>{getStreakMessage(currentStreak)}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Prayer Streak</Text>
      </View>

      <Animated.View style={[styles.currentStreakContainer, streakAnimatedStyle]}>
        <Text style={styles.streakEmoji}>{getStreakEmoji(currentStreak)}</Text>
        <View style={styles.streakInfo}>
          <Text style={styles.currentStreakNumber}>{currentStreak}</Text>
          <Text style={styles.currentStreakLabel}>Current Streak</Text>
        </View>
      </Animated.View>

      <Text style={styles.streakMessage}>{getStreakMessage(currentStreak)}</Text>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{longestStreak}</Text>
          <Text style={styles.statLabel}>Longest Streak</Text>
        </View>

        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{totalPrayersCompleted}</Text>
          <Text style={styles.statLabel}>Completed</Text>
        </View>

        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{completionRate}%</Text>
          <Text style={styles.statLabel}>Success Rate</Text>
        </View>
      </View>

      {totalPrayersSkipped > 0 && (
        <View style={styles.skippedContainer}>
          <Text style={styles.skippedText}>
            {totalPrayersSkipped} prayer{totalPrayersSkipped !== 1 ? 's' : ''} skipped
          </Text>
          <Text style={styles.encouragementText}>
            Every prayer is a chance to reconnect with Allah
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    margin: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  lockScreenContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2D3748',
  },
  currentStreakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F7FAFC',
    borderRadius: 20,
    padding: 20,
    marginBottom: 15,
  },
  streakBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  streakEmoji: {
    fontSize: 40,
    marginRight: 15,
  },
  streakInfo: {
    alignItems: 'center',
  },
  currentStreakNumber: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#4299E1',
  },
  streakNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  currentStreakLabel: {
    fontSize: 14,
    color: '#718096',
    marginTop: 4,
  },
  streakMessage: {
    fontSize: 16,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 20,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2D3748',
  },
  statLabel: {
    fontSize: 12,
    color: '#718096',
    marginTop: 4,
  },
  skippedContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#FED7D7',
    borderRadius: 10,
    alignItems: 'center',
  },
  skippedText: {
    fontSize: 14,
    color: '#C53030',
    fontWeight: '600',
    marginBottom: 5,
  },
  encouragementText: {
    fontSize: 12,
    color: '#744210',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
