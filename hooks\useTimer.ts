import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useTimerStore } from '../store/timerStore';

export const useTimer = () => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const backgroundTimeRef = useRef<Date | null>(null);
  
  const {
    isActive,
    isPaused,
    timeRemaining,
    updateTimeRemaining,
    pauseTimer,
    resumeTimer,
  } = useTimerStore();

  // Handle app state changes (background/foreground)
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        // App going to background - record current time
        if (isActive && !isPaused) {
          backgroundTimeRef.current = new Date();
        }
      } else if (nextAppState === 'active') {
        // App coming to foreground - calculate elapsed time
        if (backgroundTimeRef.current && isActive && !isPaused) {
          const now = new Date();
          const elapsedSeconds = Math.floor(
            (now.getTime() - backgroundTimeRef.current.getTime()) / 1000
          );
          
          // Update timer with elapsed time
          const newTimeRemaining = Math.max(0, timeRemaining - elapsedSeconds);
          updateTimeRemaining(newTimeRemaining);
          
          backgroundTimeRef.current = null;
        }
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
    };
  }, [isActive, isPaused, timeRemaining, updateTimeRemaining]);

  // Main timer countdown logic
  useEffect(() => {
    if (isActive && !isPaused && timeRemaining > 0) {
      intervalRef.current = setInterval(() => {
        updateTimeRemaining(timeRemaining - 1);
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isActive, isPaused, timeRemaining, updateTimeRemaining]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    isActive,
    isPaused,
    timeRemaining,
  };
};

// Utility function to format time
export const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// Utility function to format time with hours if needed
export const formatTimeWithHours = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  
  return formatTime(seconds);
};

// Get time remaining in different formats
export const useFormattedTime = () => {
  const { timeRemaining } = useTimerStore();
  
  return {
    formatted: formatTime(timeRemaining),
    formattedWithHours: formatTimeWithHours(timeRemaining),
    minutes: Math.floor(timeRemaining / 60),
    seconds: timeRemaining % 60,
    totalSeconds: timeRemaining,
  };
};
