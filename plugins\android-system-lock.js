const { withAndroidManifest, withMainActivity } = require('@expo/config-plugins');

const withAndroidSystemLock = (config) => {
  // Add permissions and launcher intent filter to AndroidManifest.xml
  config = withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;

    // Add required permissions
    const permissions = [
      'android.permission.SYSTEM_ALERT_WINDOW',
      'android.permission.WAKE_LOCK',
      'android.permission.DISABLE_KEYGUARD',
      'android.permission.REORDER_TASKS',
    ];

    permissions.forEach(permission => {
      if (!androidManifest.manifest['uses-permission']?.find(p => p.$['android:name'] === permission)) {
        androidManifest.manifest['uses-permission'] = androidManifest.manifest['uses-permission'] || [];
        androidManifest.manifest['uses-permission'].push({
          $: { 'android:name': permission }
        });
      }
    });

    // Add launcher intent filter to MainActivity
    const application = androidManifest.manifest.application[0];
    const activities = application.activity || [];

    // Find the main activity (usually the first one or the one with LAUNCHER category)
    const mainActivity = activities.find(activity => {
      const intentFilters = activity['intent-filter'] || [];
      return intentFilters.some(filter => {
        const categories = filter.category || [];
        return categories.some(cat => cat.$['android:name'] === 'android.intent.category.LAUNCHER');
      });
    });

    if (mainActivity) {
      // Add HOME intent filter for launcher functionality
      const homeIntentFilter = {
        action: [{ $: { 'android:name': 'android.intent.action.MAIN' } }],
        category: [
          { $: { 'android:name': 'android.intent.category.HOME' } },
          { $: { 'android:name': 'android.intent.category.DEFAULT' } }
        ]
      };

      // Check if HOME intent filter already exists
      const existingIntentFilters = mainActivity['intent-filter'] || [];
      const hasHomeFilter = existingIntentFilters.some(filter => {
        const categories = filter.category || [];
        return categories.some(cat => cat.$['android:name'] === 'android.intent.category.HOME');
      });

      if (!hasHomeFilter) {
        mainActivity['intent-filter'] = mainActivity['intent-filter'] || [];
        mainActivity['intent-filter'].push(homeIntentFilter);
      }
    }

    return config;
  });

  // Modify MainActivity for kiosk mode
  config = withMainActivity(config, (config) => {
    const mainActivity = config.modResults;

    // Add imports
    const imports = [
      'import android.view.WindowManager;',
      'import android.view.View;',
      'import android.os.Build;',
    ];

    imports.forEach(importStatement => {
      if (!mainActivity.contents.includes(importStatement)) {
        mainActivity.contents = mainActivity.contents.replace(
          'import android.os.Bundle;',
          `import android.os.Bundle;\n${importStatement}`
        );
      }
    });

    return config;
  });

  return config;
};

module.exports = withAndroidSystemLock;
