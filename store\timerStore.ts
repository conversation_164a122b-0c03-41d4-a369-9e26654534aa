import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { launcherService } from '../services/LauncherService';

export interface TimerState {
  // Timer state
  isActive: boolean;
  isPaused: boolean;
  timeRemaining: number; // in seconds
  totalDuration: number; // in seconds (default 15 minutes = 900 seconds)

  // Prayer context
  currentPrayer: string | null;
  prayerStartTime: Date | null;

  // Lock state
  isLocked: boolean;
  lockStartTime: Date | null;

  // Launcher mode state
  isLauncherMode: boolean;
  launcherModeSupported: boolean;

  // Exit attempt tracking
  exitAttempts: number;
  lastExitAttempt: Date | null;

  // Streak and gamification
  currentStreak: number;
  longestStreak: number;
  totalPrayersCompleted: number;
  totalPrayersSkipped: number;
  lastCompletedPrayer: Date | null;

  // Actions
  startTimer: (prayerName?: string) => void;
  pauseTimer: () => void;
  resumeTimer: () => void;
  stopTimer: (completed?: boolean) => void;
  resetTimer: () => void;
  updateTimeRemaining: (seconds: number) => void;

  // Lock actions
  activateLock: () => void;
  deactivateLock: () => void;

  // Launcher actions
  initializeLauncher: () => Promise<void>;
  activateLauncherMode: () => Promise<boolean>;
  deactivateLauncherMode: () => Promise<boolean>;

  // Exit actions
  recordExitAttempt: () => void;
  resetExitAttempts: () => void;

  // Streak management
  completePrayer: () => void;
  skipPrayer: () => void;
  resetStreak: () => void;
}

const TIMER_DURATION = 15 * 60; // 15 minutes in seconds

export const useTimerStore = create<TimerState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    isActive: false,
    isPaused: false,
    timeRemaining: TIMER_DURATION,
    totalDuration: TIMER_DURATION,
    currentPrayer: null,
    prayerStartTime: null,
    isLocked: false,
    lockStartTime: null,
    isLauncherMode: false,
    launcherModeSupported: false,
    exitAttempts: 0,
    lastExitAttempt: null,
    currentStreak: 0,
    longestStreak: 0,
    totalPrayersCompleted: 0,
    totalPrayersSkipped: 0,
    lastCompletedPrayer: null,

    // Timer actions
    startTimer: (prayerName = 'Prayer Time') => {
      const now = new Date();
      set({
        isActive: true,
        isPaused: false,
        currentPrayer: prayerName,
        prayerStartTime: now,
        timeRemaining: TIMER_DURATION,
        exitAttempts: 0,
        lastExitAttempt: null,
      });
    },

    pauseTimer: () => {
      set({ isPaused: true });
    },

    resumeTimer: () => {
      set({ isPaused: false });
    },

    stopTimer: (completed = false) => {
      const state = get();

      // Handle streak based on completion status
      if (completed && state.timeRemaining <= 0) {
        // Timer completed naturally - count as successful prayer
        get().completePrayer();
      } else if (state.isActive && state.timeRemaining > 0) {
        // Timer stopped early - count as skipped prayer
        get().skipPrayer();
      }

      set({
        isActive: false,
        isPaused: false,
        timeRemaining: TIMER_DURATION,
        currentPrayer: null,
        prayerStartTime: null,
        isLocked: false,
        lockStartTime: null,
        exitAttempts: 0,
        lastExitAttempt: null,
      });
    },

    resetTimer: () => {
      set({
        timeRemaining: TIMER_DURATION,
        exitAttempts: 0,
        lastExitAttempt: null,
      });
    },

    updateTimeRemaining: (seconds: number) => {
      set({ timeRemaining: Math.max(0, seconds) });

      // Auto-stop when timer reaches 0 (mark as completed)
      if (seconds <= 0) {
        get().stopTimer(true);
      }
    },

    // Lock actions
    activateLock: () => {
      set({
        isLocked: true,
        lockStartTime: new Date(),
      });
    },

    deactivateLock: () => {
      set({
        isLocked: false,
        lockStartTime: null,
      });
    },

    // Launcher actions
    initializeLauncher: async () => {
      try {
        await launcherService.initialize();
        const supported = await launcherService.supportsLauncherSwitching();
        const isLauncher = await launcherService.isDefaultLauncher();

        set({
          launcherModeSupported: supported,
          isLauncherMode: isLauncher,
        });
      } catch (error) {
        console.error('Failed to initialize launcher:', error);
        set({
          launcherModeSupported: false,
          isLauncherMode: false,
        });
      }
    },

    activateLauncherMode: async () => {
      try {
        const success = await launcherService.activateLauncherMode();
        if (success) {
          set({ isLauncherMode: true });
        }
        return success;
      } catch (error) {
        console.error('Failed to activate launcher mode:', error);
        return false;
      }
    },

    deactivateLauncherMode: async () => {
      try {
        const success = await launcherService.deactivateLauncherMode();
        if (success) {
          set({ isLauncherMode: false });
        }
        return success;
      } catch (error) {
        console.error('Failed to deactivate launcher mode:', error);
        return false;
      }
    },

    // Exit tracking
    recordExitAttempt: () => {
      const now = new Date();
      set((state) => ({
        exitAttempts: state.exitAttempts + 1,
        lastExitAttempt: now,
      }));
    },

    resetExitAttempts: () => {
      set({
        exitAttempts: 0,
        lastExitAttempt: null,
      });
    },

    // Streak management
    completePrayer: () => {
      const now = new Date();
      set((state) => {
        const newStreak = state.currentStreak + 1;
        const newLongestStreak = Math.max(state.longestStreak, newStreak);

        return {
          currentStreak: newStreak,
          longestStreak: newLongestStreak,
          totalPrayersCompleted: state.totalPrayersCompleted + 1,
          lastCompletedPrayer: now,
        };
      });
    },

    skipPrayer: () => {
      set((state) => ({
        currentStreak: 0, // Reset streak when prayer is skipped
        totalPrayersSkipped: state.totalPrayersSkipped + 1,
      }));
    },

    resetStreak: () => {
      set({
        currentStreak: 0,
      });
    },
  }))
);

// Selectors for common state combinations
export const useTimerProgress = () => {
  const { timeRemaining, totalDuration } = useTimerStore();
  return {
    progress: (totalDuration - timeRemaining) / totalDuration,
    percentage: Math.round(
      ((totalDuration - timeRemaining) / totalDuration) * 100
    ),
    timeRemaining,
    totalDuration,
  };
};

export const useTimerStatus = () => {
  const { isActive, isPaused, isLocked } = useTimerStore();
  return {
    isActive,
    isPaused,
    isLocked,
    isRunning: isActive && !isPaused,
  };
};

export const usePrayerContext = () => {
  const { currentPrayer, prayerStartTime, exitAttempts } = useTimerStore();
  return {
    currentPrayer,
    prayerStartTime,
    exitAttempts,
    hasExitAttempts: exitAttempts > 0,
  };
};
