import React, { useState, useEffect } from 'react';
import { StyleSheet, Pressable, Alert, ScrollView, Modal } from 'react-native';
import { Text, View } from '@/components/Themed';
import {
  useTimerStore,
  useTimerStatus,
  usePrayerContext,
} from '../../store/timerStore';
import {
  TimerDisplay,
  SimpleTimerDisplay,
  CompactTimerDisplay,
} from '../../components/TimerDisplay';
import { SystemLockScreen } from '../../components/SystemLockScreen';
import { EnhancedSoftLockScreen } from '../../components/EnhancedSoftLockScreen';
import { StreakDisplay } from '../../components/StreakDisplay';
import { PrayerTimeSettings } from '../../components/PrayerTimeSettings';
import { useTimer } from '../../hooks/useTimer';
import { prayerTimeService, PrayerTimes } from '../../services/PrayerTimeService';
import { prayerNotificationService } from '../../services/PrayerNotificationService';

export default function PrayerLockScreen() {
  const [showLockScreen, setShowLockScreen] = useState(false);
  const [useEnhancedLock, setUseEnhancedLock] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [prayerTimes, setPrayerTimes] = useState<PrayerTimes | null>(null);
  const [nextPrayer, setNextPrayer] = useState<{ name: string; time: string; timeUntil: number } | null>(null);
  const [currentLocation, setCurrentLocation] = useState<string>('');

  const {
    startTimer,
    stopTimer,
    pauseTimer,
    resumeTimer,
    activateLock,
    deactivateLock,
  } = useTimerStore();
  const { isActive, isPaused, isRunning } = useTimerStatus();
  const { currentPrayer, exitAttempts } = usePrayerContext();

  // Initialize timer hook
  useTimer();

  // Initialize services and load prayer times
  useEffect(() => {
    initializeServices();
  }, []);

  const initializeServices = async () => {
    try {
      // Initialize prayer time service
      await prayerTimeService.initialize();

      // Initialize notification service
      await prayerNotificationService.initialize();

      // Load current prayer times
      await loadPrayerTimes();

      console.log('All services initialized');
    } catch (error) {
      console.error('Failed to initialize services:', error);
      Alert.alert('Initialization Error', 'Some features may not work properly. Please check your settings.');
    }
  };

  const loadPrayerTimes = async () => {
    try {
      const times = prayerTimeService.getCurrentPrayerTimes();
      setPrayerTimes(times);

      const next = prayerTimeService.getNextPrayer();
      setNextPrayer(next);

      // Get location info
      const location = await prayerTimeService.getCurrentLocation();
      if (location) {
        setCurrentLocation(`${location.city || 'Unknown'}, ${location.country || 'Unknown'}`);
      }
    } catch (error) {
      console.error('Failed to load prayer times:', error);
    }
  };

  const handleStartPrayer = (prayerName?: string) => {
    const prayer = prayerName || nextPrayer?.name || 'Prayer';
    startTimer(`${prayer} Prayer`);
    activateLock();
    setShowLockScreen(true);
  };

  const handleStartNextPrayer = () => {
    if (nextPrayer) {
      handleStartPrayer(nextPrayer.name);
    } else {
      Alert.alert('No Prayer Time', 'No upcoming prayer time found. Please check your prayer time settings.');
    }
  };

  const handleStartManualPrayer = () => {
    Alert.alert(
      'Select Prayer',
      'Which prayer would you like to start?',
      [
        { text: 'Fajr', onPress: () => handleStartPrayer('Fajr') },
        { text: 'Dhuhr', onPress: () => handleStartPrayer('Dhuhr') },
        { text: 'Asr', onPress: () => handleStartPrayer('Asr') },
        { text: 'Maghrib', onPress: () => handleStartPrayer('Maghrib') },
        { text: 'Isha', onPress: () => handleStartPrayer('Isha') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleStopTimer = () => {
    stopTimer();
    deactivateLock();
    setShowLockScreen(false);
  };

  const handleExitLock = () => {
    setShowLockScreen(false);
    deactivateLock();
  };

  const handleSettingsUpdated = () => {
    loadPrayerTimes();
  };

  const formatTimeUntil = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const handleTestLock = () => {
    if (!isActive) {
      Alert.alert('No Timer', 'Please start a timer first');
      return;
    }
    setShowLockScreen(true);
  };

  return (
    <View style={styles.container}>
      {/* Lock Screen Modal */}
      {showLockScreen && (
        <Modal
          visible={showLockScreen}
          animationType="fade"
          presentationStyle="fullScreen"
        >
          {useEnhancedLock ? (
            <EnhancedSoftLockScreen onExit={handleExitLock} />
          ) : (
            <SystemLockScreen onExit={handleExitLock} />
          )}
        </Modal>
      )}

      {/* Settings Modal */}
      <Modal
        visible={showSettings}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <PrayerTimeSettings
          onClose={() => setShowSettings(false)}
          onSettingsUpdated={handleSettingsUpdated}
        />
      </Modal>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.title}>Taqwa App</Text>
            <Text style={styles.subtitle}>Prayer Time Lock System</Text>
            {currentLocation && (
              <Text style={styles.locationText}>{currentLocation}</Text>
            )}
          </View>
          <Pressable style={styles.settingsButton} onPress={() => setShowSettings(true)}>
            <Text style={styles.settingsButtonText}>⚙️</Text>
          </Pressable>
        </View>

        {/* Next Prayer Info */}
        {nextPrayer && (
          <View style={styles.nextPrayerSection}>
            <Text style={styles.nextPrayerTitle}>Next Prayer</Text>
            <View style={styles.nextPrayerInfo}>
              <View>
                <Text style={styles.nextPrayerName}>{nextPrayer.name}</Text>
                <Text style={styles.nextPrayerTime}>{nextPrayer.time}</Text>
              </View>
              <View style={styles.nextPrayerCountdown}>
                <Text style={styles.countdownText}>in {formatTimeUntil(nextPrayer.timeUntil)}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Prayer Times Display */}
        {prayerTimes && (
          <View style={styles.prayerTimesSection}>
            <Text style={styles.sectionTitle}>Today's Prayer Times</Text>
            <View style={styles.prayerTimesGrid}>
              {Object.entries(prayerTimes).map(([prayer, time]) => (
                prayer !== 'sunrise' && prayer !== 'sunset' && (
                  <View key={prayer} style={styles.prayerTimeItem}>
                    <Text style={styles.prayerName}>{prayer.charAt(0).toUpperCase() + prayer.slice(1)}</Text>
                    <Text style={styles.prayerTime}>{time}</Text>
                  </View>
                )
              ))}
            </View>
          </View>
        )}

        {/* Timer Display */}
        {isActive && (
          <View style={styles.timerSection}>
            <Text style={styles.sectionTitle}>Prayer Timer</Text>
            <View style={styles.timerContainer}>
              <TimerDisplay
                size={200}
                strokeWidth={8}
                color="#10B981"
                backgroundColor="#374151"
                textColor="#F9FAFB"
              />
            </View>
          </View>
        )}

        {/* Streak Display */}
        <StreakDisplay />

        {/* Status Info */}
        <View style={styles.statusSection}>
          <Text style={styles.statusText}>
            Status: {isActive ? (isPaused ? 'Paused' : 'Running') : 'Ready'}
          </Text>
          {currentPrayer && (
            <Text style={styles.prayerText}>Prayer: {currentPrayer}</Text>
          )}
          {exitAttempts > 0 && (
            <Text style={styles.attemptsText}>
              Exit Attempts: {exitAttempts}
            </Text>
          )}
        </View>

        {/* Control Buttons */}
        <View style={styles.buttonSection}>
          {!isActive ? (
            <View style={styles.startButtonsContainer}>
              <Pressable style={styles.primaryButton} onPress={handleStartNextPrayer}>
                <Text style={styles.primaryButtonText}>
                  {nextPrayer ? `Start ${nextPrayer.name} Prayer` : 'Start Prayer'}
                </Text>
              </Pressable>

              <Pressable style={styles.secondaryButton} onPress={handleStartManualPrayer}>
                <Text style={styles.secondaryButtonText}>Choose Prayer</Text>
              </Pressable>
            </View>
          ) : (
            <View style={styles.controlButtons}>
              <Pressable
                style={styles.controlButton}
                onPress={isPaused ? resumeTimer : pauseTimer}
              >
                <Text style={styles.controlButtonText}>
                  {isPaused ? 'Resume' : 'Pause'}
                </Text>
              </Pressable>

              <Pressable style={styles.stopButton} onPress={handleStopTimer}>
                <Text style={styles.stopButtonText}>Stop</Text>
              </Pressable>
            </View>
          )}

          {isActive && (
            <Pressable style={styles.testLockButton} onPress={handleTestLock}>
              <Text style={styles.testLockButtonText}>Show Lock Screen</Text>
            </Pressable>
          )}

          {/* Settings and Options */}
          <View style={styles.optionsRow}>
            <Pressable
              style={styles.optionButton}
              onPress={() => setUseEnhancedLock(!useEnhancedLock)}
            >
              <Text style={styles.optionButtonText}>
                {useEnhancedLock ? '🔒 Enhanced' : '📱 Basic'} Lock
              </Text>
            </Pressable>
          </View>
        </View>


      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#111827',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingBottom: 40,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#F9FAFB',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#9CA3AF',
    marginBottom: 4,
  },
  locationText: {
    fontSize: 14,
    color: '#6B7280',
  },
  settingsButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#374151',
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingsButtonText: {
    fontSize: 20,
  },
  nextPrayerSection: {
    backgroundColor: '#1F2937',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  nextPrayerTitle: {
    fontSize: 16,
    color: '#9CA3AF',
    marginBottom: 8,
  },
  nextPrayerInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  nextPrayerName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#F9FAFB',
  },
  nextPrayerTime: {
    fontSize: 18,
    color: '#10B981',
  },
  nextPrayerCountdown: {
    alignItems: 'flex-end',
  },
  countdownText: {
    fontSize: 16,
    color: '#F59E0B',
    fontWeight: '600',
  },
  prayerTimesSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#F9FAFB',
    marginBottom: 12,
  },
  prayerTimesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  prayerTimeItem: {
    backgroundColor: '#1F2937',
    borderRadius: 8,
    padding: 12,
    width: '48%',
    marginBottom: 8,
    alignItems: 'center',
  },
  prayerName: {
    fontSize: 14,
    color: '#9CA3AF',
    marginBottom: 4,
  },
  prayerTime: {
    fontSize: 16,
    fontWeight: '600',
    color: '#F9FAFB',
  },
  separator: {
    marginVertical: 30,
    height: 1,
    width: '80%',
  },
  timerSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  placeholderTimer: {
    width: 200,
    height: 200,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 18,
    fontWeight: '600',
    opacity: 0.7,
  },
  placeholderSubtext: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
    fontFamily: 'monospace',
  },
  statusSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  prayerText: {
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 4,
  },
  attemptsText: {
    fontSize: 14,
    color: '#F59E0B',
  },
  buttonSection: {
    alignItems: 'center',
    gap: 16,
    marginTop: 20,
    marginBottom: 40, // Extra space before bottom nav
  },
  startButton: {
    backgroundColor: '#10B981',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  controlButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  controlButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  controlButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  stopButton: {
    backgroundColor: '#EF4444',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  stopButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  testLockButton: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  testLockButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  toggleButton: {
    backgroundColor: '#6B7280',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  toggleButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  resetButton: {
    backgroundColor: '#F59E0B',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  resetButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  startButtonsContainer: {
    gap: 12,
    marginBottom: 16,
    width: '100%',
  },
  primaryButton: {
    backgroundColor: '#10B981',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: '#374151',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#F9FAFB',
    fontSize: 16,
    fontWeight: '500',
  },
  optionsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 12,
  },
  optionButton: {
    backgroundColor: '#374151',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  optionButtonText: {
    color: '#9CA3AF',
    fontSize: 14,
    fontWeight: '500',
  },
  timerContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
});
