import React, { useState } from 'react';
import { StyleSheet, Pressable, Alert, ScrollView } from 'react-native';
import { Text, View } from '@/components/Themed';
import {
  useTimerStore,
  useTimerStatus,
  usePrayerContext,
} from '../../store/timerStore';
import {
  TimerDisplay,
  SimpleTimerDisplay,
  CompactTimerDisplay,
} from '../../components/TimerDisplay';
import { SystemLockScreen } from '../../components/SystemLockScreen';
import { EnhancedSoftLockScreen } from '../../components/EnhancedSoftLockScreen';
import { StreakDisplay } from '../../components/StreakDisplay';
import { useTimer } from '../../hooks/useTimer';

export default function TimerTestScreen() {
  const [showLockScreen, setShowLockScreen] = useState(false);
  const [useEnhancedLock, setUseEnhancedLock] = useState(true);

  const {
    startTimer,
    stopTimer,
    pauseTimer,
    resumeTimer,
    activateLock,
    deactivateLock,
  } = useTimerStore();
  const { isActive, isPaused, isRunning } = useTimerStatus();
  const { currentPrayer, exitAttempts } = usePrayerContext();

  // Initialize timer hook
  useTimer();

  const handleStartPrayer = () => {
    startTimer('Dhuhr Prayer');
    activateLock();
    setShowLockScreen(true);
  };

  const handleStopTimer = () => {
    stopTimer();
    deactivateLock();
    setShowLockScreen(false);
  };

  const handleExitLock = () => {
    setShowLockScreen(false);
    deactivateLock();
  };

  const handleTestLock = () => {
    if (!isActive) {
      Alert.alert('No Timer', 'Please start a timer first');
      return;
    }
    setShowLockScreen(true);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.title}>Taqwa Prayer Timer</Text>
        <Text style={styles.subtitle}>Soft Lock Mechanism Test</Text>

        <View
          style={styles.separator}
          lightColor="#eee"
          darkColor="rgba(255,255,255,0.1)"
        />

        {/* Timer Display */}
        <View style={styles.timerSection}>
          {isActive ? (
            <TimerDisplay size={200} />
          ) : (
            <View style={styles.placeholderTimer}>
              <Text style={styles.placeholderText}>Timer Ready</Text>
              <Text style={styles.placeholderSubtext}>15:00</Text>
            </View>
          )}
        </View>

        {/* Streak Display */}
        <StreakDisplay />

        {/* Status Info */}
        <View style={styles.statusSection}>
          <Text style={styles.statusText}>
            Status: {isActive ? (isPaused ? 'Paused' : 'Running') : 'Stopped'}
          </Text>
          {currentPrayer && (
            <Text style={styles.prayerText}>Prayer: {currentPrayer}</Text>
          )}
          {exitAttempts > 0 && (
            <Text style={styles.attemptsText}>
              Exit Attempts: {exitAttempts}
            </Text>
          )}
        </View>

        {/* Control Buttons */}
        <View style={styles.buttonSection}>
          {!isActive ? (
            <Pressable style={styles.startButton} onPress={handleStartPrayer}>
              <Text style={styles.startButtonText}>Start Prayer Timer</Text>
            </Pressable>
          ) : (
            <View style={styles.controlButtons}>
              <Pressable
                style={styles.controlButton}
                onPress={isPaused ? resumeTimer : pauseTimer}
              >
                <Text style={styles.controlButtonText}>
                  {isPaused ? 'Resume' : 'Pause'}
                </Text>
              </Pressable>

              <Pressable style={styles.stopButton} onPress={handleStopTimer}>
                <Text style={styles.stopButtonText}>Stop</Text>
              </Pressable>
            </View>
          )}

          {isActive && (
            <Pressable style={styles.testLockButton} onPress={handleTestLock}>
              <Text style={styles.testLockButtonText}>Test Lock Screen</Text>
            </Pressable>
          )}

          {/* Reset Button - Always Available */}
          <Pressable
            style={styles.resetButton}
            onPress={() => {
              stopTimer();
              setShowLockScreen(false);
              deactivateLock();
            }}
          >
            <Text style={styles.resetButtonText}>Reset Timer</Text>
          </Pressable>

          {/* Lock Type Toggle */}
          <Pressable
            style={styles.toggleButton}
            onPress={() => setUseEnhancedLock(!useEnhancedLock)}
          >
            <Text style={styles.toggleButtonText}>
              {useEnhancedLock ? 'Enhanced Lock' : 'System Lock'}
            </Text>
          </Pressable>
        </View>

        {/* Lock Screens */}
        {useEnhancedLock ? (
          <EnhancedSoftLockScreen
            visible={showLockScreen}
            onExit={handleExitLock}
          />
        ) : (
          <SystemLockScreen visible={showLockScreen} onExit={handleExitLock} />
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingHorizontal: 20,
    paddingTop: 40,
    paddingBottom: 120, // Add extra space for bottom navigation
    minHeight: '100%',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  separator: {
    marginVertical: 30,
    height: 1,
    width: '80%',
  },
  timerSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  placeholderTimer: {
    width: 200,
    height: 200,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 18,
    fontWeight: '600',
    opacity: 0.7,
  },
  placeholderSubtext: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
    fontFamily: 'monospace',
  },
  statusSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  prayerText: {
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 4,
  },
  attemptsText: {
    fontSize: 14,
    color: '#F59E0B',
  },
  buttonSection: {
    alignItems: 'center',
    gap: 16,
    marginTop: 20,
    marginBottom: 40, // Extra space before bottom nav
  },
  startButton: {
    backgroundColor: '#10B981',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  controlButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  controlButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  controlButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  stopButton: {
    backgroundColor: '#EF4444',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  stopButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  testLockButton: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  testLockButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  toggleButton: {
    backgroundColor: '#6B7280',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  toggleButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  resetButton: {
    backgroundColor: '#F59E0B',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  resetButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
});
