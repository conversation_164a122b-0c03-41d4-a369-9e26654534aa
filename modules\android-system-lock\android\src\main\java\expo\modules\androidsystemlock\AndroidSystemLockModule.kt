package expo.modules.androidsystemlock

import android.app.Activity
import android.app.ActivityManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.view.View
import android.view.WindowManager
import expo.modules.kotlin.modules.Module
import expo.modules.kotlin.modules.ModuleDefinition
import expo.modules.kotlin.Promise

class AndroidSystemLockModule : Module() {
  private var isKioskModeActive = false
  private var originalSystemUiVisibility = 0

  override fun definition() = ModuleDefinition {
    Name("AndroidSystemLock")

    AsyncFunction("checkOverlayPermission") { promise: Promise ->
      val context = appContext.reactContext ?: return@AsyncFunction promise.reject("NO_CONTEXT", "No context available", null)
      
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        val hasPermission = Settings.canDrawOverlays(context)
        promise.resolve(mapOf("granted" to hasPermission))
      } else {
        promise.resolve(mapOf("granted" to true))
      }
    }

    AsyncFunction("requestOverlayPermission") { promise: Promise ->
      val context = appContext.reactContext ?: return@AsyncFunction promise.reject("NO_CONTEXT", "No context available", null)
      
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        if (!Settings.canDrawOverlays(context)) {
          val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:${context.packageName}"))
          intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
          context.startActivity(intent)
          promise.resolve(false) // User needs to grant permission manually
        } else {
          promise.resolve(true)
        }
      } else {
        promise.resolve(true)
      }
    }

    AsyncFunction("enableKioskMode") { promise: Promise ->
      val activity = appContext.currentActivity ?: return@AsyncFunction promise.reject("NO_ACTIVITY", "No activity available", null)
      
      activity.runOnUiThread {
        try {
          // Store original system UI visibility
          originalSystemUiVisibility = activity.window.decorView.systemUiVisibility
          
          // Enable immersive mode
          val flags = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                  or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                  or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                  or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                  or View.SYSTEM_UI_FLAG_FULLSCREEN
                  or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
          
          activity.window.decorView.systemUiVisibility = flags
          
          // Keep screen on
          activity.window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
          
          // Prevent screenshots (optional security measure)
          activity.window.setFlags(
            WindowManager.LayoutParams.FLAG_SECURE,
            WindowManager.LayoutParams.FLAG_SECURE
          )
          
          isKioskModeActive = true
          promise.resolve(true)
        } catch (e: Exception) {
          promise.reject("KIOSK_ERROR", "Failed to enable kiosk mode: ${e.message}", e)
        }
      }
    }

    AsyncFunction("disableKioskMode") { promise: Promise ->
      val activity = appContext.currentActivity ?: return@AsyncFunction promise.reject("NO_ACTIVITY", "No activity available", null)
      
      activity.runOnUiThread {
        try {
          // Restore original system UI visibility
          activity.window.decorView.systemUiVisibility = originalSystemUiVisibility
          
          // Remove keep screen on flag
          activity.window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
          
          // Remove secure flag
          activity.window.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
          
          isKioskModeActive = false
          promise.resolve(true)
        } catch (e: Exception) {
          promise.reject("KIOSK_ERROR", "Failed to disable kiosk mode: ${e.message}", e)
        }
      }
    }

    AsyncFunction("isKioskModeActive") { promise: Promise ->
      promise.resolve(isKioskModeActive)
    }

    AsyncFunction("preventTaskSwitcher") { promise: Promise ->
      val activity = appContext.currentActivity ?: return@AsyncFunction promise.reject("NO_ACTIVITY", "No activity available", null)

      activity.runOnUiThread {
        try {
          // This is a more aggressive approach - requires careful implementation
          // Override onBackPressed, onKeyDown, etc. in the activity
          promise.resolve(true)
        } catch (e: Exception) {
          promise.reject("TASK_SWITCHER_ERROR", "Failed to prevent task switcher: ${e.message}", e)
        }
      }
    }

    AsyncFunction("bringAppToForeground") { promise: Promise ->
      val activity = appContext.currentActivity ?: return@AsyncFunction promise.reject("NO_ACTIVITY", "No activity available", null)

      activity.runOnUiThread {
        try {
          // Method 1: Use Intent to bring app to foreground
          val intent = Intent(activity, activity::class.java)
          intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
          activity.startActivity(intent)

          // Method 2: Move task to front using ActivityManager
          val activityManager = activity.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
          activityManager.moveTaskToFront(activity.taskId, ActivityManager.MOVE_TASK_WITH_HOME)

          // Method 3: If we have overlay permission, create a temporary overlay to force focus
          if (Settings.canDrawOverlays(activity)) {
            createTemporaryOverlay(activity)
          }

          promise.resolve(true)
        } catch (e: Exception) {
          promise.reject("BRING_TO_FOREGROUND_ERROR", "Failed to bring app to foreground: ${e.message}", e)
        }
      }
    }

    private fun createTemporaryOverlay(activity: Activity) {
      try {
        val windowManager = activity.getSystemService(Context.WINDOW_SERVICE) as WindowManager

        // Create a small, invisible overlay that will bring focus back to our app
        val params = WindowManager.LayoutParams(
          1, 1,
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
          } else {
            WindowManager.LayoutParams.TYPE_PHONE
          },
          WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
          android.graphics.PixelFormat.TRANSLUCENT
        )

        val overlayView = View(activity)
        windowManager.addView(overlayView, params)

        // Remove the overlay after a short delay
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
          try {
            windowManager.removeView(overlayView)
          } catch (e: Exception) {
            // Ignore removal errors
          }
        }, 100)

      } catch (e: Exception) {
        // Ignore overlay creation errors
      }
    }

    // Launcher-related functions
    AsyncFunction("isDefaultLauncher") { promise: Promise ->
      val context = appContext.reactContext ?: return@AsyncFunction promise.reject("NO_CONTEXT", "No context available", null)

      try {
        val intent = Intent(Intent.ACTION_MAIN)
        intent.addCategory(Intent.CATEGORY_HOME)

        val resolveInfo = context.packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)
        val isDefault = resolveInfo?.activityInfo?.packageName == context.packageName

        promise.resolve(isDefault)
      } catch (e: Exception) {
        promise.reject("LAUNCHER_ERROR", "Failed to check default launcher: ${e.message}", e)
      }
    }

    AsyncFunction("getCurrentLauncher") { promise: Promise ->
      val context = appContext.reactContext ?: return@AsyncFunction promise.reject("NO_CONTEXT", "No context available", null)

      try {
        val intent = Intent(Intent.ACTION_MAIN)
        intent.addCategory(Intent.CATEGORY_HOME)

        val resolveInfo = context.packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)
        val currentLauncher = resolveInfo?.activityInfo?.packageName ?: "unknown"

        promise.resolve(mapOf(
          "packageName" to currentLauncher,
          "className" to (resolveInfo?.activityInfo?.name ?: "unknown")
        ))
      } catch (e: Exception) {
        promise.reject("LAUNCHER_ERROR", "Failed to get current launcher: ${e.message}", e)
      }
    }

    AsyncFunction("promptLauncherSelection") { promise: Promise ->
      val context = appContext.reactContext ?: return@AsyncFunction promise.reject("NO_CONTEXT", "No context available", null)

      try {
        val intent = Intent(Intent.ACTION_MAIN)
        intent.addCategory(Intent.CATEGORY_HOME)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

        // This will show the launcher selection dialog
        val chooser = Intent.createChooser(intent, "Select Home App")
        chooser.flags = Intent.FLAG_ACTIVITY_NEW_TASK

        context.startActivity(chooser)
        promise.resolve(true)
      } catch (e: Exception) {
        promise.reject("LAUNCHER_ERROR", "Failed to prompt launcher selection: ${e.message}", e)
      }
    }

    AsyncFunction("openLauncherSettings") { promise: Promise ->
      val context = appContext.reactContext ?: return@AsyncFunction promise.reject("NO_CONTEXT", "No context available", null)

      try {
        val intent = Intent(Settings.ACTION_HOME_SETTINGS)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

        context.startActivity(intent)
        promise.resolve(true)
      } catch (e: Exception) {
        // Fallback to general settings if home settings not available
        try {
          val fallbackIntent = Intent(Settings.ACTION_SETTINGS)
          fallbackIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
          context.startActivity(fallbackIntent)
          promise.resolve(true)
        } catch (fallbackException: Exception) {
          promise.reject("LAUNCHER_ERROR", "Failed to open launcher settings: ${e.message}", e)
        }
      }
    }

    AsyncFunction("supportsLauncherSwitching") { promise: Promise ->
      val context = appContext.reactContext ?: return@AsyncFunction promise.reject("NO_CONTEXT", "No context available", null)

      try {
        val intent = Intent(Intent.ACTION_MAIN)
        intent.addCategory(Intent.CATEGORY_HOME)

        val resolveInfos = context.packageManager.queryIntentActivities(intent, 0)
        val supportsLaunchers = resolveInfos.size > 1 // More than one launcher available

        promise.resolve(supportsLaunchers)
      } catch (e: Exception) {
        promise.reject("LAUNCHER_ERROR", "Failed to check launcher support: ${e.message}", e)
      }
    }

    AsyncFunction("getAllLaunchers") { promise: Promise ->
      val context = appContext.reactContext ?: return@AsyncFunction promise.reject("NO_CONTEXT", "No context available", null)

      try {
        val intent = Intent(Intent.ACTION_MAIN)
        intent.addCategory(Intent.CATEGORY_HOME)

        val resolveInfos = context.packageManager.queryIntentActivities(intent, 0)
        val launchers = resolveInfos.map { resolveInfo ->
          mapOf(
            "packageName" to resolveInfo.activityInfo.packageName,
            "className" to resolveInfo.activityInfo.name,
            "label" to resolveInfo.loadLabel(context.packageManager).toString()
          )
        }

        promise.resolve(launchers)
      } catch (e: Exception) {
        promise.reject("LAUNCHER_ERROR", "Failed to get all launchers: ${e.message}", e)
      }
    }
  }
}
