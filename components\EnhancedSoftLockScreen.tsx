import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Modal,
  StatusBar,
  BackHandler,
  Platform,
  Alert,
  AppState,
  Dimensions,
  Vibration,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withRepeat,
} from 'react-native-reanimated';
import { activateKeepAwakeAsync, deactivateKeepAwake } from 'expo-keep-awake';
import {
  useTimerStore,
  useTimerStatus,
  usePrayerContext,
} from '../store/timerStore';
import { TimerDisplay } from './TimerDisplay';
import { useTimer } from '../hooks/useTimer';
import { audioService, SpiritualContent } from '../services/AudioService';
import { smartSoftLockService } from '../services/SmartSoftLockService';

interface EnhancedSoftLockScreenProps {
  visible: boolean;
  onExit?: () => void;
}

export const EnhancedSoftLockScreen: React.FC<EnhancedSoftLockScreenProps> = ({
  visible,
  onExit,
}) => {
  const [showExitConfirmation, setShowExitConfirmation] = useState(false);
  const [longPressProgress, setLongPressProgress] = useState(0);
  const [currentContent, setCurrentContent] = useState<SpiritualContent | null>(
    null
  );
  const [isAdhanPlaying, setIsAdhanPlaying] = useState(false);
  const [showGuiltMessage, setShowGuiltMessage] = useState(false);
  const [guiltMessage, setGuiltMessage] = useState('');

  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const progressTimer = useRef<NodeJS.Timeout | null>(null);
  const contentUpdateTimer = useRef<NodeJS.Timeout | null>(null);

  const { stopTimer, recordExitAttempt } = useTimerStore();
  const { isRunning } = useTimerStatus();
  const { currentPrayer, exitAttempts } = usePrayerContext();

  // Initialize timer hook
  useTimer();

  // Animation values
  const exitButtonScale = useSharedValue(1);
  const exitButtonOpacity = useSharedValue(0.7);
  const progressWidth = useSharedValue(0);
  const contentFade = useSharedValue(1);
  const screenDim = useSharedValue(0.3); // Dim the screen for focus

  // Initialize audio and keep screen awake when lock becomes visible
  useEffect(() => {
    if (visible && isRunning) {
      initializeLockSession();
    } else {
      cleanupLockSession();
    }

    return () => {
      cleanupLockSession();
    };
  }, [visible, isRunning]);

  const initializeLockSession = async () => {
    try {
      // Keep screen awake during prayer
      await activateKeepAwakeAsync();

      // Start smart soft lock for persistent monitoring
      await smartSoftLockService.activateSoftLock(() => {
        console.log('User returned during prayer - lock screen is active');
        // The lock screen is already visible, just log the return
      });

      // Start Adhan playback
      setIsAdhanPlaying(true);
      await audioService.playAdhan();
      setIsAdhanPlaying(false);

      // Start content rotation
      startContentRotation();

      // Dim screen slightly for focus
      screenDim.value = withTiming(0.3, { duration: 1000 });

      console.log('Lock session initialized with background monitoring');
    } catch (error) {
      console.error('Failed to initialize lock session:', error);
    }
  };

  const cleanupLockSession = async () => {
    try {
      // Stop smart soft lock
      await smartSoftLockService.deactivateSoftLock();

      // Stop keeping screen awake
      deactivateKeepAwake();

      // Cleanup audio
      await audioService.cleanup();

      // Clear timers
      if (contentUpdateTimer.current) {
        clearInterval(contentUpdateTimer.current);
      }

      // Reset screen brightness
      screenDim.value = withTiming(0, { duration: 500 });

      console.log('Lock session cleaned up with smart soft lock stopped');
    } catch (error) {
      console.error('Failed to cleanup lock session:', error);
    }
  };

  const startContentRotation = () => {
    // Update content immediately
    updateCurrentContent();

    // Then update every 30 seconds
    contentUpdateTimer.current = setInterval(() => {
      updateCurrentContent();
    }, 30000);
  };

  const updateCurrentContent = () => {
    const content = audioService.getCurrentContent();
    if (content) {
      // Fade out, update content, fade in
      contentFade.value = withSequence(
        withTiming(0, { duration: 300 }),
        withTiming(1, { duration: 300 })
      );

      setTimeout(() => {
        setCurrentContent(content);
      }, 300);
    }
  };

  // Handle hardware back button (Android)
  useEffect(() => {
    if (!visible || Platform.OS !== 'android') return;

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        handleExitAttempt();
        return true; // Prevent default back behavior
      }
    );

    return () => backHandler.remove();
  }, [visible, exitAttempts]);

  // Monitor app state to detect when user tries to leave
  useEffect(() => {
    if (!visible || !isRunning) return;

    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        console.log('User tried to leave app during prayer time');

        // Record exit attempt
        handleExitAttempt();
      } else if (nextAppState === 'active') {
        // User returned to app - show guilt message if timer still running
        if (isRunning && exitAttempts > 0) {
          showGuiltPrompt();

          // Get soft lock state
          const lockState = smartSoftLockService.getState();
          console.log('Smart soft lock state:', lockState);
        }
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );
    return () => subscription?.remove();
  }, [visible, isRunning, exitAttempts]);

  const handleExitAttempt = () => {
    recordExitAttempt();

    // Vibrate to get attention
    Vibration.vibrate([0, 200, 100, 200]);

    // Show guilt message
    const message = audioService.getExitAttemptMessage(exitAttempts + 1);
    setGuiltMessage(message);
    setShowGuiltMessage(true);

    // Hide guilt message after 3 seconds
    setTimeout(() => {
      setShowGuiltMessage(false);
    }, 3000);
  };

  const showGuiltPrompt = () => {
    const message = audioService.getRandomGuiltMessage();
    Alert.alert('Return to Prayer', message, [
      {
        text: 'Continue Prayer',
        style: 'default',
      },
      {
        text: 'End Prayer Session',
        style: 'destructive',
        onPress: handleForceExit,
      },
    ]);
  };

  const handleLongPressStart = () => {
    console.log('Long press started');
    // Start long press progress
    setLongPressProgress(0);

    let progress = 0;
    const increment = 100 / 30; // 3 seconds = 30 intervals of 100ms

    progressTimer.current = setInterval(() => {
      progress += increment;

      // Cap progress at 100%
      if (progress >= 100) {
        progress = 100;
        setLongPressProgress(100);
        console.log('Long press progress: 100 (completed)');

        if (progressTimer.current) {
          clearInterval(progressTimer.current);
          progressTimer.current = null;
        }
        console.log('Long press completed - showing confirmation');
        handleLongPressComplete();
      } else {
        setLongPressProgress(progress);
        console.log('Long press progress:', Math.round(progress));
      }
    }, 100);

    // Animate button
    exitButtonScale.value = withTiming(0.95, { duration: 100 });
    exitButtonOpacity.value = withTiming(1, { duration: 100 });
  };

  const handleLongPressEnd = () => {
    console.log('Long press ended');
    // Cancel long press
    if (progressTimer.current) {
      clearInterval(progressTimer.current);
      progressTimer.current = null;
    }

    console.log('Resetting long press timer');
    setLongPressProgress(0);

    // Reset button animation
    exitButtonScale.value = withTiming(1, { duration: 200 });
    exitButtonOpacity.value = withTiming(0.7, { duration: 200 });
  };

  const handleLongPressComplete = () => {
    console.log('Long press completed - showing confirmation dialog');

    // Reset button animation first
    exitButtonScale.value = withTiming(1, { duration: 200 });
    exitButtonOpacity.value = withTiming(0.7, { duration: 200 });

    // Show confirmation dialog immediately
    handleConfirmExit();
  };

  const handleConfirmExit = () => {
    console.log('User confirmed exit');
    const guiltMessage = audioService.getRandomGuiltMessage();

    Alert.alert('Are You Sure?', guiltMessage, [
      {
        text: 'Stay in Prayer',
        style: 'default',
        onPress: () => setShowExitConfirmation(false),
      },
      {
        text: 'End Session',
        style: 'destructive',
        onPress: handleForceExit,
      },
    ]);
  };

  const handleForceExit = () => {
    console.log('Force exit - stopping timer and closing lock screen');
    setShowExitConfirmation(false);
    stopTimer();
    onExit?.();
  };

  // Animated styles
  const exitButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: exitButtonScale.value }],
    opacity: exitButtonOpacity.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: contentFade.value,
  }));

  const screenOverlayStyle = useAnimatedStyle(() => ({
    backgroundColor: `rgba(0, 0, 0, ${screenDim.value})`,
  }));

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="fade"
      presentationStyle="fullScreen"
      statusBarTranslucent
    >
      <StatusBar hidden />

      {/* Screen dimming overlay */}
      <Animated.View style={[StyleSheet.absoluteFill, screenOverlayStyle]} />

      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.prayerTitle}>
            {currentPrayer || 'Prayer Time'}
          </Text>
          <Text style={styles.subtitle}>
            {isAdhanPlaying ? 'Listen to the Adhan...' : 'Focus on your prayer'}
          </Text>
        </View>

        {/* Timer Display */}
        <View style={styles.timerContainer}>
          <TimerDisplay size={250} strokeWidth={12} />
        </View>

        {/* Spiritual Content */}
        <Animated.View style={[styles.contentContainer, contentAnimatedStyle]}>
          {currentContent && (
            <>
              {currentContent.arabic && (
                <Text style={styles.arabicText}>{currentContent.arabic}</Text>
              )}
              <Text style={styles.translationText}>
                {currentContent.translation}
              </Text>
              {currentContent.reference && (
                <Text style={styles.referenceText}>
                  — {currentContent.reference}
                </Text>
              )}
            </>
          )}
        </Animated.View>

        {/* Exit Attempts Counter */}
        {exitAttempts > 0 && (
          <View style={styles.exitAttemptsContainer}>
            <Text style={styles.exitAttemptsText}>
              Exit attempts: {exitAttempts}
            </Text>
          </View>
        )}

        {/* Guilt Message Overlay */}
        {showGuiltMessage && (
          <View style={styles.guiltMessageOverlay}>
            <Text style={styles.guiltMessageText}>{guiltMessage}</Text>
          </View>
        )}

        {/* Long Press Exit Button */}
        <View style={styles.exitButtonContainer}>
          <Animated.View style={exitButtonAnimatedStyle}>
            <Pressable
              style={styles.exitButton}
              onPressIn={handleLongPressStart}
              onPressOut={handleLongPressEnd}
            >
              <Text style={styles.exitButtonText}>Hold to Exit Prayer</Text>

              {/* Progress indicator */}
              {longPressProgress > 0 && (
                <View style={styles.progressContainer}>
                  <View
                    style={[
                      styles.progressBar,
                      { width: `${longPressProgress}%` },
                    ]}
                  />
                </View>
              )}
            </Pressable>
          </Animated.View>

          <Text style={styles.exitHint}>Hold for 3 seconds to exit</Text>
        </View>
      </View>
    </Modal>
  );
};

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
  },
  prayerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#B0B0B0',
    textAlign: 'center',
  },
  timerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingVertical: 30,
    alignItems: 'center',
    minHeight: 120,
  },
  arabicText: {
    fontSize: 24,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 15,
    fontFamily: Platform.OS === 'ios' ? 'Arial' : 'serif',
    lineHeight: 36,
  },
  translationText: {
    fontSize: 16,
    color: '#E0E0E0',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 10,
  },
  referenceText: {
    fontSize: 14,
    color: '#A0A0A0',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  exitAttemptsContainer: {
    backgroundColor: 'rgba(255, 0, 0, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 10,
  },
  exitAttemptsText: {
    color: '#FF6B6B',
    fontSize: 14,
    fontWeight: '600',
  },
  guiltMessageOverlay: {
    position: 'absolute',
    top: '50%',
    left: 20,
    right: 20,
    backgroundColor: 'rgba(255, 0, 0, 0.9)',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    transform: [{ translateY: -50 }],
  },
  guiltMessageText: {
    color: '#FFFFFF',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '600',
    lineHeight: 22,
  },
  exitButtonContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  exitButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    minWidth: 200,
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  exitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  progressContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#FF6B6B',
  },
  exitHint: {
    color: '#A0A0A0',
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
});
