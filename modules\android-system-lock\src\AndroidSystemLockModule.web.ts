export default {
  async checkOverlayPermission() {
    return { granted: false };
  },
  async requestOverlayPermission() {
    return false;
  },
  async enableKioskMode() {
    console.log('Kiosk mode not supported on web');
    return false;
  },
  async disableKioskMode() {
    console.log('Kiosk mode not supported on web');
    return false;
  },
  async isKioskModeActive() {
    return false;
  },
  async preventTaskSwitcher() {
    return false;
  },
  // Launcher functions (not supported on web)
  async isDefaultLauncher() {
    console.log('Launcher functions not supported on web');
    return false;
  },
  async getCurrentLauncher() {
    console.log('Launcher functions not supported on web');
    return { packageName: 'web', className: 'web' };
  },
  async promptLauncherSelection() {
    console.log('Launcher functions not supported on web');
    return false;
  },
  async openLauncherSettings() {
    console.log('Launcher functions not supported on web');
    return false;
  },
  async supportsLauncherSwitching() {
    console.log('Launcher functions not supported on web');
    return false;
  },
  async getAllLaunchers() {
    console.log('Launcher functions not supported on web');
    return [];
  },
};
