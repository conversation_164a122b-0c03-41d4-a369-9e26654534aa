import { NativeModulesProxy } from 'expo-modules-core';

// Import the native module. On web, it will be resolved to AndroidSystemLock.web.ts
// and on native platforms to AndroidSystemLock.ts
import AndroidSystemLockModule from './AndroidSystemLockModule';

export interface OverlayPermissionResult {
  granted: boolean;
}

export interface LauncherInfo {
  packageName: string;
  className: string;
  label?: string;
}

// Overlay/Kiosk functions
export async function checkOverlayPermission(): Promise<OverlayPermissionResult> {
  return await AndroidSystemLockModule.checkOverlayPermission();
}

export async function requestOverlayPermission(): Promise<boolean> {
  return await AndroidSystemLockModule.requestOverlayPermission();
}

export async function enableKioskMode(): Promise<boolean> {
  return await AndroidSystemLockModule.enableKioskMode();
}

export async function disableKioskMode(): Promise<boolean> {
  return await AndroidSystemLockModule.disableKioskMode();
}

export async function isKioskModeActive(): Promise<boolean> {
  return await AndroidSystemLockModule.isKioskModeActive();
}

export async function preventTaskSwitcher(): Promise<boolean> {
  return await AndroidSystemLockModule.preventTaskSwitcher();
}

export async function bringAppToForeground(): Promise<boolean> {
  return await AndroidSystemLockModule.bringAppToForeground();
}

// Launcher functions
export async function isDefaultLauncher(): Promise<boolean> {
  return await AndroidSystemLockModule.isDefaultLauncher();
}

export async function getCurrentLauncher(): Promise<LauncherInfo> {
  return await AndroidSystemLockModule.getCurrentLauncher();
}

export async function promptLauncherSelection(): Promise<boolean> {
  return await AndroidSystemLockModule.promptLauncherSelection();
}

export async function openLauncherSettings(): Promise<boolean> {
  return await AndroidSystemLockModule.openLauncherSettings();
}

export async function supportsLauncherSwitching(): Promise<boolean> {
  return await AndroidSystemLockModule.supportsLauncherSwitching();
}

export async function getAllLaunchers(): Promise<LauncherInfo[]> {
  return await AndroidSystemLockModule.getAllLaunchers();
}
