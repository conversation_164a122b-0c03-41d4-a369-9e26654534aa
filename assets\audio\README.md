# Audio Assets for Taqwa App

## Adhan Audio Files

To add Adhan audio to the app, place your audio files in this directory and update the AudioService.

### Required Files

1. **adhan.mp3** - Main Adhan audio file
   - Format: MP3
   - Duration: 3-5 minutes typical
   - Quality: 128kbps or higher recommended

### How to Add Adhan Audio

1. **Download Adhan Audio**
   - You can download from: https://www.islamicfinder.org/adhan/
   - Or use any royalty-free Adhan recording
   - Ensure it's in MP3 format

2. **Add to Project**
   - Place the file as `TaqwaApp/assets/audio/adhan.mp3`
   - The file will be automatically bundled with the app

3. **Update AudioService**
   - In `services/AudioService.ts`, uncomment the lines:
   ```typescript
   const { sound } = await Audio.Sound.createAsync(
     require('../assets/audio/adhan.mp3'),
     { shouldPlay: true, volume: 0.8, isLooping: false }
   );
   this.sound = sound;
   ```

4. **Test the Audio**
   - Start a prayer timer
   - The <PERSON>han should play automatically
   - After completion, spiritual content rotation begins

### Audio Permissions

The app already includes the necessary audio permissions:
- Background audio playback
- Silent mode playback (iOS)
- Audio focus management (Android)

### Troubleshooting

- **Audio not playing**: Check file format and path
- **Audio cuts off**: Ensure background audio permissions
- **Poor quality**: Use higher bitrate MP3 files

### Alternative Sources

Free Adhan audio sources:
- Islamic Finder: https://www.islamicfinder.org/adhan/
- Freesound: https://freesound.org/ (search "adhan")
- Islamic audio libraries

### File Size Considerations

- Keep file size under 5MB for app performance
- Consider using compressed MP3 format
- Test on different devices for compatibility
