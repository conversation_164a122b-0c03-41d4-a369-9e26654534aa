import { Platform, Alert } from 'react-native';
import * as AndroidSystemLock from '../modules/android-system-lock/src/AndroidSystemLock';
import { StorageService } from './StorageService';

export interface LauncherState {
  isLauncherMode: boolean;
  originalLauncher: {
    packageName: string;
    className: string;
    label?: string;
  } | null;
  launcherActivatedAt: Date | null;
}

class LauncherService {
  private state: LauncherState = {
    isLauncherMode: false,
    originalLauncher: null,
    launcherActivatedAt: null,
  };

  /**
   * Initialize the launcher service and restore state if needed
   */
  async initialize(): Promise<void> {
    if (Platform.OS !== 'android') {
      return;
    }

    try {
      // Restore state from storage
      const savedState =
        StorageService.getObject<LauncherState>('launcher_state');
      if (savedState) {
        this.state = { ...this.state, ...savedState };
      }

      // Check if we're currently the default launcher
      const isDefault = await AndroidSystemLock.isDefaultLauncher();
      this.state.isLauncherMode = isDefault;

      // If we're the launcher but don't have saved state, get current launcher info
      if (isDefault && !this.state.originalLauncher) {
        await this.detectAndSaveOriginalLauncher();
      }
    } catch (error) {
      console.error('Failed to initialize launcher service:', error);
    }
  }

  /**
   * Check if the device supports launcher switching
   */
  async supportsLauncherSwitching(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      return await AndroidSystemLock.supportsLauncherSwitching();
    } catch (error) {
      console.error('Failed to check launcher support:', error);
      return false;
    }
  }

  /**
   * Get current launcher state
   */
  getState(): LauncherState {
    return { ...this.state };
  }

  /**
   * Check if app is currently the default launcher
   */
  async isDefaultLauncher(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      const isDefault = await AndroidSystemLock.isDefaultLauncher();
      this.state.isLauncherMode = isDefault;
      await this.saveState();
      return isDefault;
    } catch (error) {
      console.error('Failed to check default launcher:', error);
      return false;
    }
  }

  /**
   * Activate launcher mode for prayer lock
   */
  async activateLauncherMode(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      console.log('Launcher mode not supported on this platform');
      return false;
    }

    try {
      // Check if launcher switching is supported
      const supportsLaunchers = await this.supportsLauncherSwitching();
      if (!supportsLaunchers) {
        throw new Error('Device does not support multiple launchers');
      }

      // Save current launcher before switching
      await this.detectAndSaveOriginalLauncher();

      // Check if we're already the default launcher
      const isAlreadyDefault = await AndroidSystemLock.isDefaultLauncher();
      if (isAlreadyDefault) {
        this.state.isLauncherMode = true;
        this.state.launcherActivatedAt = new Date();
        await this.saveState();
        return true;
      }

      // Show explanation and prompt user to set as launcher
      return new Promise((resolve) => {
        Alert.alert(
          'Prayer Lock Setup',
          'To enable true prayer lock, please set TaqwaApp as your home launcher. This will prevent exiting during prayer time.\n\nYou can restore your original launcher when prayer ends.',
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => resolve(false),
            },
            {
              text: 'Set as Launcher',
              onPress: async () => {
                try {
                  await AndroidSystemLock.promptLauncherSelection();

                  // Give user time to select launcher, then check if they selected us
                  setTimeout(async () => {
                    const isNowDefault =
                      await AndroidSystemLock.isDefaultLauncher();
                    if (isNowDefault) {
                      this.state.isLauncherMode = true;
                      this.state.launcherActivatedAt = new Date();
                      await this.saveState();
                      resolve(true);
                    } else {
                      resolve(false);
                    }
                  }, 2000);
                } catch (error) {
                  console.error('Failed to prompt launcher selection:', error);
                  resolve(false);
                }
              },
            },
          ]
        );
      });
    } catch (error) {
      console.error('Failed to activate launcher mode:', error);
      return false;
    }
  }

  /**
   * Deactivate launcher mode and guide user to restore original launcher
   */
  async deactivateLauncherMode(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return true;
    }

    try {
      const isCurrentlyDefault = await AndroidSystemLock.isDefaultLauncher();
      if (!isCurrentlyDefault) {
        // Already not the default launcher
        this.state.isLauncherMode = false;
        this.state.launcherActivatedAt = null;
        await this.saveState();
        return true;
      }

      // Guide user to restore original launcher
      return new Promise((resolve) => {
        const originalLauncherName =
          this.state.originalLauncher?.label || 'your original launcher';

        Alert.alert(
          'Restore Launcher',
          `Prayer time completed! Please restore ${originalLauncherName} as your home launcher to return to normal phone usage.`,
          [
            {
              text: 'Keep TaqwaApp',
              style: 'cancel',
              onPress: () => resolve(false),
            },
            {
              text: 'Restore Launcher',
              onPress: async () => {
                try {
                  await AndroidSystemLock.promptLauncherSelection();

                  // Give user time to select launcher, then check if they changed it
                  setTimeout(async () => {
                    const isStillDefault =
                      await AndroidSystemLock.isDefaultLauncher();
                    if (!isStillDefault) {
                      this.state.isLauncherMode = false;
                      this.state.launcherActivatedAt = null;
                      await this.saveState();
                      resolve(true);
                    } else {
                      resolve(false);
                    }
                  }, 2000);
                } catch (error) {
                  console.error(
                    'Failed to prompt launcher restoration:',
                    error
                  );
                  resolve(false);
                }
              },
            },
          ]
        );
      });
    } catch (error) {
      console.error('Failed to deactivate launcher mode:', error);
      return false;
    }
  }

  /**
   * Open launcher settings for manual configuration
   */
  async openLauncherSettings(): Promise<void> {
    if (Platform.OS !== 'android') {
      return;
    }

    try {
      await AndroidSystemLock.openLauncherSettings();
    } catch (error) {
      console.error('Failed to open launcher settings:', error);
      Alert.alert(
        'Settings Error',
        'Could not open launcher settings. Please go to Settings > Apps > Default Apps > Home app manually.',
        [{ text: 'OK' }]
      );
    }
  }

  /**
   * Get all available launchers on the device
   */
  async getAvailableLaunchers(): Promise<any[]> {
    if (Platform.OS !== 'android') {
      return [];
    }

    try {
      return await AndroidSystemLock.getAllLaunchers();
    } catch (error) {
      console.error('Failed to get available launchers:', error);
      return [];
    }
  }

  /**
   * Detect and save the current launcher (before switching to ours)
   */
  private async detectAndSaveOriginalLauncher(): Promise<void> {
    try {
      const currentLauncher = await AndroidSystemLock.getCurrentLauncher();

      // Only save if it's not our app
      if (currentLauncher.packageName !== 'com.talha_sde.TaqwaApp') {
        this.state.originalLauncher = currentLauncher;
        await this.saveState();
      }
    } catch (error) {
      console.error('Failed to detect original launcher:', error);
    }
  }

  /**
   * Save current state to persistent storage
   */
  private async saveState(): Promise<void> {
    try {
      StorageService.setObject('launcher_state', this.state);
    } catch (error) {
      console.error('Failed to save launcher state:', error);
    }
  }

  /**
   * Clear saved state
   */
  async clearState(): Promise<void> {
    this.state = {
      isLauncherMode: false,
      originalLauncher: null,
      launcherActivatedAt: null,
    };

    try {
      StorageService.remove('launcher_state');
    } catch (error) {
      console.error('Failed to clear launcher state:', error);
    }
  }
}

// Export singleton instance
export const launcherService = new LauncherService();
