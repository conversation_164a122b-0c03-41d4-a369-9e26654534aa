import { Audio } from 'expo-av';

export interface SpiritualContent {
  id: string;
  type: 'hadith' | 'quran' | 'dua' | 'reminder';
  arabic?: string;
  translation: string;
  reference?: string;
  audio?: string; // URL or local asset
  category: 'prayer_importance' | 'missing_prayer' | 'akhirah' | 'taqwa' | 'general';
}

export interface AudioServiceState {
  isPlaying: boolean;
  currentTrack: string | null;
  volume: number;
  isLoaded: boolean;
}

class AudioService {
  private sound: Audio.Sound | null = null;
  private isInitialized = false;
  private currentContent: SpiritualContent | null = null;
  private contentRotationIndex = 0;

  // Comprehensive spiritual content - categorized for better rotation
  private spiritualContent: SpiritualContent[] = [
    // Prayer Importance
    {
      id: '1',
      type: 'hadith',
      translation:
        'The Prophet (ﷺ) said: "Prayer is the pillar of religion and whoever abandons it demolishes the pillar of religion."',
      reference: 'Bayhaqi',
      category: 'prayer_importance',
    },
    {
      id: '2',
      type: 'quran',
      arabic:
        'وَأَقِيمُوا الصَّلَاةَ وَآتُوا الزَّكَاةَ وَارْكَعُوا مَعَ الرَّاكِعِينَ',
      translation:
        'And establish prayer and give zakah and bow with those who bow.',
      reference: 'Quran 2:43',
      category: 'prayer_importance',
    },
    {
      id: '3',
      type: 'quran',
      arabic: 'إِنَّ الصَّلَاةَ تَنْهَىٰ عَنِ الْفَحْشَاءِ وَالْمُنكَرِ',
      translation: 'Indeed, prayer prohibits immorality and wrongdoing.',
      reference: 'Quran 29:45',
      category: 'prayer_importance',
    },
    // Missing Prayer Consequences
    {
      id: '4',
      type: 'hadith',
      translation:
        'The Prophet (ﷺ) said: "Between a man and disbelief is the abandonment of prayer."',
      reference: 'Muslim',
      category: 'missing_prayer',
    },
    {
      id: '5',
      type: 'hadith',
      arabic: 'مَن تَرَكَ صَلاةً مُتَعَمِّداً فَقَد بَرِئَت مِنهُ ذِمَّةُ اللهِ',
      translation: 'Whoever abandons prayer intentionally, Allah\'s protection is removed from him.',
      reference: 'Ibn Majah',
      category: 'missing_prayer',
    },
    {
      id: '6',
      type: 'quran',
      arabic: 'فَوَيْلٌ لِّلْمُصَلِّينَ الَّذِينَ هُمْ عَن صَلَاتِهِمْ سَاهُونَ',
      translation: 'So woe to those who pray but are heedless of their prayer.',
      reference: 'Quran 107:4-5',
      category: 'missing_prayer',
    },
    // Akhirah (Afterlife) Reminders
    {
      id: '7',
      type: 'hadith',
      translation:
        'The Prophet (ﷺ) said: "The first matter that the slave will be brought to account for on the Day of Judgment is the prayer."',
      reference: 'Tirmidhi',
      category: 'akhirah',
    },
    {
      id: '8',
      type: 'quran',
      arabic: 'وَمَا هَٰذِهِ الْحَيَاةُ الدُّنْيَا إِلَّا لَهْوٌ وَلَعِبٌ ۚ وَإِنَّ الدَّارَ الْآخِرَةَ لَهِيَ الْحَيَوَانُ',
      translation: 'And this worldly life is not but diversion and amusement. And indeed, the home of the Hereafter - that is the [eternal] life.',
      reference: 'Quran 29:64',
      category: 'akhirah',
    },
    {
      id: '9',
      type: 'quran',
      arabic: 'كُلُّ نَفْسٍ ذَائِقَةُ الْمَوْتِ',
      translation: 'Every soul will taste death.',
      reference: 'Quran 3:185',
      category: 'akhirah',
    },
    // Taqwa and God-consciousness
    {
      id: '10',
      type: 'quran',
      arabic: 'وَتَزَوَّدُوا فَإِنَّ خَيْرَ الزَّادِ التَّقْوَىٰ',
      translation: 'And take provisions, but indeed, the best provision is Taqwa (God-consciousness).',
      reference: 'Quran 2:197',
      category: 'taqwa',
    },
    {
      id: '11',
      type: 'hadith',
      arabic: 'اتَّقِ اللهَ حَيْثُمَا كُنْتَ',
      translation: 'Fear Allah wherever you are.',
      reference: 'Tirmidhi',
      category: 'taqwa',
    },
    // Dua
    {
      id: '12',
      type: 'dua',
      arabic:
        'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ',
      translation:
        'Our Lord, give us good in this world and good in the next world, and save us from the punishment of the Fire.',
      reference: 'Quran 2:201',
      category: 'general',
    },
    // Modern Reminders
    {
      id: '13',
      type: 'reminder',
      translation: 'Your phone can wait. Allah cannot. Focus on your prayer.',
      reference: 'Taqwa App Reminder',
      category: 'general',
    },
    {
      id: '14',
      type: 'reminder',
      translation: 'This moment with Allah is more valuable than any notification.',
      reference: 'Taqwa App Reminder',
      category: 'general',
    },
    {
      id: '15',
      type: 'reminder',
      translation: 'Remember: You will be asked about this prayer on the Day of Judgment.',
      reference: 'Taqwa App Reminder',
      category: 'general',
    },
  ];

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Configure audio session for background playback
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: true,
        interruptionModeIOS: 1, // DO_NOT_MIX
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        interruptionModeAndroid: 1, // DO_NOT_MIX
        playThroughEarpieceAndroid: false,
      });

      this.isInitialized = true;
      console.log('AudioService initialized');
    } catch (error) {
      console.error('Failed to initialize AudioService:', error);
      throw error;
    }
  }

  async playAdhan(): Promise<void> {
    try {
      await this.initialize();

      // In production, you would load actual Adhan audio file
      // For now, we'll simulate with a placeholder
      console.log('Playing Adhan...');

      // You would load and play actual Adhan audio here:
      // const { sound } = await Audio.Sound.createAsync(
      //   require('../assets/audio/adhan.mp3'),
      //   { shouldPlay: true, volume: 0.8 }
      // );
      // this.sound = sound;

      // Try to load actual Adhan audio file
      try {
        // Uncomment and add actual adhan.mp3 file to assets/audio/ folder
        const { sound } = await Audio.Sound.createAsync(
          require('../assets/audio/aadhan.mp3'),
          { shouldPlay: true, volume: 0.8, isLooping: false }
        );
        this.sound = sound;

        // For now, simulate Adhan with a shorter duration for testing
        console.log('Simulating Adhan playback (no audio file found)');
        setTimeout(() => {
          console.log('Adhan completed');
          this.onAdhanComplete();
        }, 5000); // 5 seconds for demo
      } catch (audioError) {
        console.log('No Adhan audio file found, using simulation');
        setTimeout(() => {
          console.log('Adhan completed (simulated)');
          this.onAdhanComplete();
        }, 5000);
      }
    } catch (error) {
      console.error('Failed to play Adhan:', error);
    }
  }

  private onAdhanComplete(): void {
    // Start rotating spiritual content after Adhan
    this.startContentRotation();
  }

  private startContentRotation(): void {
    // Rotate spiritual content every 30 seconds
    this.rotateContent();
    setInterval(() => {
      this.rotateContent();
    }, 30000);
  }

  private rotateContent(): void {
    this.currentContent = this.spiritualContent[this.contentRotationIndex];
    this.contentRotationIndex =
      (this.contentRotationIndex + 1) % this.spiritualContent.length;
    console.log('Rotating to content:', this.currentContent.translation);
  }

  getCurrentContent(): SpiritualContent | null {
    return this.currentContent;
  }

  getRandomGuiltMessage(): string {
    const guiltMessages = [
      'Allah is watching. Are you sure you want to leave during prayer time?',
      'The angels are recording your deeds. Will you abandon your prayer?',
      'Remember: Prayer is better than sleep. Stay focused.',
      'This is your time with Allah. Do not let distractions take you away.',
      'The Prophet (ﷺ) never missed his prayers. Follow his example.',
      "Your phone will be here after prayer. Allah's time is more precious.",
      "Every moment of prayer brings you closer to Allah. Don't waste it.",
      'The world can wait. Your Creator cannot.',
    ];

    return guiltMessages[Math.floor(Math.random() * guiltMessages.length)];
  }

  getExitAttemptMessage(attemptCount: number): string {
    if (attemptCount === 1) {
      return 'First attempt to leave prayer. Remember, Allah sees everything.';
    } else if (attemptCount === 2) {
      return 'Second attempt. The Shaytan is trying to distract you. Stay strong.';
    } else if (attemptCount >= 3) {
      return 'Multiple attempts to leave. Please reflect on the importance of prayer.';
    }
    return 'Stay focused on your prayer.';
  }

  async setVolume(volume: number): Promise<void> {
    if (this.sound) {
      await this.sound.setVolumeAsync(Math.max(0, Math.min(1, volume)));
    }
  }

  async pause(): Promise<void> {
    if (this.sound) {
      await this.sound.pauseAsync();
    }
  }

  async resume(): Promise<void> {
    if (this.sound) {
      await this.sound.playAsync();
    }
  }

  async stop(): Promise<void> {
    if (this.sound) {
      await this.sound.stopAsync();
      await this.sound.unloadAsync();
      this.sound = null;
    }
  }

  async cleanup(): Promise<void> {
    await this.stop();
    this.currentContent = null;
    this.contentRotationIndex = 0;
    console.log('AudioService cleaned up');
  }
}

export const audioService = new AudioService();
