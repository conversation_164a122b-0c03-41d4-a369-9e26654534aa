{"name": "android-system-lock", "version": "1.0.0", "description": "Native Android system lock module for Taqwa App", "main": "build/index.js", "types": "build/index.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test"}, "keywords": ["react-native", "expo", "android", "system-lock", "kiosk-mode"], "repository": "https://github.com/your-username/taqwa-app", "author": "Taqwa App Team", "license": "MIT", "homepage": "https://github.com/your-username/taqwa-app", "devDependencies": {"expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}}