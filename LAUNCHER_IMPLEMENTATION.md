# Launcher-Based Prayer Lock Implementation

## Overview
Instead of using overlays or kiosk mode, we'll implement a launcher-based approach where the app temporarily becomes the default launcher during prayer time. This provides a true "lock" experience where swiping up or pressing home returns to the prayer app.

## How It Works

### Current Approach (Overlay/Kiosk)
- Uses system overlay permissions
- Attempts to prevent task switching
- Can be bypassed by swiping up or using recent apps
- Limited effectiveness on modern Android versions

### New Approach (Launcher-Based)
1. **Setup Phase**: App declares itself as a potential launcher in manifest
2. **Prayer Start**: Guide user to set app as default launcher
3. **Lock Active**: App becomes the home screen - all home gestures return to prayer
4. **Prayer End**: Guide user to restore original launcher

## Technical Implementation

### 1. Android Manifest Changes
```xml
<activity
    android:name=".MainActivity"
    android:exported="true"
    android:launchMode="singleTask"
    android:theme="@style/Theme.App.SplashScreen">
    
    <!-- Standard launcher intent -->
    <intent-filter android:priority="1">
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
    </intent-filter>
    
    <!-- Home launcher intent (for prayer lock) -->
    <intent-filter android:priority="1">
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.HOME" />
        <category android:name="android.intent.category.DEFAULT" />
    </intent-filter>
</activity>
```

### 2. Native Module Functions Needed
```typescript
interface LauncherService {
  // Check if app is currently the default launcher
  isDefaultLauncher(): Promise<boolean>;
  
  // Get the current default launcher package name
  getCurrentLauncher(): Promise<string>;
  
  // Trigger launcher selection dialog
  promptLauncherSelection(): Promise<void>;
  
  // Open launcher settings for manual selection
  openLauncherSettings(): Promise<void>;
  
  // Check if device supports launcher switching
  supportsLauncherSwitching(): Promise<boolean>;
}
```

### 3. User Flow

#### Starting Prayer (Launcher Activation)
1. User starts prayer timer
2. App checks if it's already the default launcher
3. If not, show explanation dialog about launcher switching
4. Guide user through launcher selection process
5. Once set as launcher, activate prayer lock screen
6. Store original launcher info for restoration

#### During Prayer
- App acts as home screen
- All home gestures return to prayer screen
- Emergency access still available
- Exit requires long-press confirmation as before

#### Ending Prayer (Launcher Restoration)
1. Prayer timer completes or user exits
2. Show restoration dialog
3. Guide user to restore original launcher
4. Provide fallback if automatic restoration fails

### 4. Implementation Steps

#### Phase 1: Core Infrastructure
- [ ] Add HOME intent filter to manifest
- [ ] Create LauncherService native module
- [ ] Implement launcher detection functions

#### Phase 2: User Interface
- [ ] Create launcher setup flow screens
- [ ] Add restoration guidance screens
- [ ] Update existing lock screen for launcher mode

#### Phase 3: Integration
- [ ] Integrate with existing timer system
- [ ] Replace overlay-based locking
- [ ] Add proper error handling and fallbacks

#### Phase 4: Testing & Polish
- [ ] Test on various Android versions
- [ ] Handle edge cases and device variations
- [ ] Add user education and onboarding

## Advantages Over Current Approach

1. **True Lock**: Impossible to bypass with gestures
2. **System Integration**: Works with Android's natural behavior
3. **Better UX**: No overlay permissions needed
4. **Reliable**: Doesn't depend on system overlay capabilities
5. **Emergency Access**: Still allows emergency calls and notifications

## Considerations

1. **User Education**: Need clear explanation of launcher switching
2. **Restoration**: Must ensure users can always restore original launcher
3. **Device Compatibility**: Some devices may have restrictions
4. **User Consent**: Requires explicit user action to set as launcher

## Fallback Strategy

If launcher switching fails or isn't supported:
1. Fall back to current overlay approach
2. Show clear messaging about limitations
3. Provide alternative lock mechanisms

## Security & Privacy

- No additional permissions required beyond current implementation
- User maintains full control over launcher selection
- No persistent system changes without user consent
- Clear restoration path always available
