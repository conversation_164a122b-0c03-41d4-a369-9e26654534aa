import { AppState, AppStateStatus, Platform, Alert } from 'react-native';
import * as Notifications from 'expo-notifications';

export interface BackgroundMonitorState {
  isMonitoring: boolean;
  prayerActive: boolean;
  reopenAttempts: number;
  lastBackgroundTime: Date | null;
}

class BackgroundMonitorService {
  private isMonitoring = false;
  private prayerActive = false;
  private reopenAttempts = 0;
  private lastBackgroundTime: Date | null = null;
  private appStateSubscription: any = null;
  private reopenTimer: any = null;
  private onAppReopenCallback: (() => void) | null = null;
  private notificationId: string | null = null;

  /**
   * Initialize background monitoring for prayer time
   */
  async initialize(): Promise<void> {
    try {
      // Set up app state monitoring
      this.setupAppStateMonitoring();

      console.log('BackgroundMonitorService initialized (simplified)');
    } catch (error) {
      console.error('Failed to initialize BackgroundMonitorService:', error);
    }
  }

  /**
   * Start monitoring during prayer time
   */
  async startPrayerMonitoring(onAppReopen?: () => void): Promise<void> {
    console.log(
      'Starting prayer monitoring - app will show notifications if minimized'
    );

    this.prayerActive = true;
    this.isMonitoring = true;
    this.reopenAttempts = 0;
    this.onAppReopenCallback = onAppReopen || null;

    console.log('Prayer monitoring started (AppState-based)');
  }

  /**
   * Stop monitoring when prayer ends
   */
  async stopPrayerMonitoring(): Promise<void> {
    console.log('Stopping prayer monitoring');

    this.prayerActive = false;
    this.isMonitoring = false;
    this.reopenAttempts = 0;
    this.onAppReopenCallback = null;

    // Clear any pending reopen timer
    if (this.reopenTimer) {
      clearTimeout(this.reopenTimer);
      this.reopenTimer = null;
    }

    // Dismiss any active notifications
    if (this.notificationId) {
      await Notifications.dismissNotificationAsync(this.notificationId);
      this.notificationId = null;
    }
  }

  /**
   * Set up app state change monitoring
   */
  private setupAppStateMonitoring(): void {
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange.bind(this)
    );
  }

  /**
   * Handle app state changes
   */
  private handleAppStateChange(nextAppState: AppStateStatus): void {
    if (!this.prayerActive || !this.isMonitoring) return;

    console.log('App state changed to:', nextAppState, 'during prayer');

    if (nextAppState === 'background' || nextAppState === 'inactive') {
      // App went to background during prayer
      this.lastBackgroundTime = new Date();
      this.reopenAttempts++;

      console.log(
        `Prayer active: App backgrounded (attempt ${this.reopenAttempts})`
      );

      // Immediately try to reopen after short delay
      this.scheduleAppReopen();
    } else if (nextAppState === 'active') {
      // App came back to foreground
      this.lastBackgroundTime = null;

      if (this.reopenAttempts > 0) {
        console.log('App reopened during prayer monitoring');

        // Trigger callback to show lock screen
        if (this.onAppReopenCallback) {
          this.onAppReopenCallback();
        }
      }
    }
  }

  /**
   * Schedule app reopen after short delay
   */
  private scheduleAppReopen(): void {
    // Clear any existing timer
    if (this.reopenTimer) {
      clearTimeout(this.reopenTimer);
    }

    // Schedule reopen after 1 second
    this.reopenTimer = setTimeout(() => {
      this.triggerAppReopen();
    }, 1000);
  }

  /**
   * Trigger app reopen using various methods
   */
  private async triggerAppReopen(): Promise<void> {
    if (!this.prayerActive) return;

    try {
      console.log('Attempting to reopen app during prayer time');

      // Method 1: Show notification to bring user back
      await this.showReopenNotification();

      // Method 2: For Android, try to use system overlay (if available)
      if (Platform.OS === 'android') {
        await this.tryAndroidReopen();
      }
    } catch (error) {
      console.error('Failed to trigger app reopen:', error);
    }
  }

  /**
   * Show notification to bring user back to app
   */
  private async showReopenNotification(): Promise<void> {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Prayer Time Active',
          body: 'Please return to complete your prayer session',
          sound: true,
          priority: Notifications.AndroidNotificationPriority.MAX,
          sticky: true,
        },
        trigger: null, // Show immediately
      });

      console.log('Reopen notification sent');
    } catch (error) {
      console.error('Failed to show reopen notification:', error);
    }
  }

  /**
   * Android-specific reopen methods
   */
  private async tryAndroidReopen(): Promise<void> {
    if (Platform.OS !== 'android') return;

    try {
      // For now, we'll use notifications to guide user back
      // Native foreground methods require complex permissions
      console.log('Android reopen: Using notification-based approach');

      // Show a more urgent notification
      await this.showUrgentReopenNotification();
    } catch (error) {
      console.error('Android reopen failed:', error);
    }
  }

  /**
   * Show urgent notification to bring user back
   */
  private async showUrgentReopenNotification(): Promise<void> {
    try {
      this.notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '🕌 Return to Prayer',
          body: 'Your prayer session is active. Please return to the app.',
          sound: true,
          priority: Notifications.AndroidNotificationPriority.MAX,
          sticky: true,
          autoDismiss: false,
          vibrate: [0, 250, 250, 250],
        },
        trigger: null,
      });

      console.log('Urgent reopen notification sent');
    } catch (error) {
      console.error('Failed to show urgent notification:', error);
    }
  }

  /**
   * Get current monitoring state
   */
  getState(): BackgroundMonitorState {
    return {
      isMonitoring: this.isMonitoring,
      prayerActive: this.prayerActive,
      reopenAttempts: this.reopenAttempts,
      lastBackgroundTime: this.lastBackgroundTime,
    };
  }

  /**
   * Cleanup when service is destroyed
   */
  async cleanup(): Promise<void> {
    await this.stopPrayerMonitoring();

    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }

    console.log('BackgroundMonitorService cleaned up');
  }
}

export const backgroundMonitorService = new BackgroundMonitorService();
