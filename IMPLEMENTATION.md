# Taqwa App - Implementation Documentation

## Project Overview

Islamic Prayer Lock App MVP - Android-first development with soft lock mechanism for prayer times.

## Tech Stack

- **Framework**: Expo with React Native
- **Navigation**: Expo Router (file-based routing)
- **State Management**: Zustand
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **Data Fetching**: TanStack Query
- **Animations**: React Native Reanimated
- **Forms**: React Hook Form + Zod validation
- **Storage**: React Native MMKV (secure storage)
- **Audio**: Expo AV
- **Background Tasks**: Expo Background Fetch + Task Manager
- **Keep Awake**: Expo Keep Awake
- **Notifications**: Expo Notifications
- **Error Tracking**: Sentry
- **UI Components**: Gorhom Bottom Sheet

## Project Structure

```
TaqwaApp/
├── app/                    # Expo Router pages
│   ├── (tabs)/            # Tab navigation
│   ├── _layout.tsx        # Root layout
│   └── modal.tsx          # Modal screens
├── components/            # Reusable components
├── hooks/                 # Custom hooks
├── store/                 # Zustand stores
├── services/              # API and background services
├── utils/                 # Utility functions
├── constants/             # App constants
└── assets/               # Images, fonts, audio files
```

## MVP Features Implementation Status

### ✅ Phase 1: Project Setup

- [x] Expo project with TypeScript and Navigation template
- [x] Install core dependencies (Zustand, TanStack Query, etc.)
- [x] Project structure setup

### ✅ Phase 2: Core Soft Lock Mechanism

- [x] Basic timer component (15-minute countdown)
- [x] Soft lock screen overlay
- [x] Exit functionality with long-press (FIXED - confirmation dialog now works)
- [x] Confirmation dialog with guilt messaging (FIXED - proper modal display)
- [x] Background timer management
- [x] Zustand store for timer state
- [x] Custom hooks for timer logic
- [x] Animated timer display components
- [x] Full-screen soft lock modal
- [x] Test screen for functionality validation

### ✅ Phase 2.5: Android System Lock Enhancement

- [x] Android overlay service architecture
- [x] System-wide lock permissions handling
- [x] Enhanced SystemLockScreen component
- [x] Back button prevention (Android hardware back button blocked)
- [x] Emergency access functionality
- [x] Kiosk mode simulation
- [x] Permission request flow for overlay access

### ⏳ Phase 3: Audio Integration

- [ ] Audio service setup
- [ ] Adhaan playback at lock start
- [ ] Background audio management
- [ ] Quranic verse audio rotation

### ⏳ Phase 4: Prayer Time Management

- [ ] Manual prayer time input
- [ ] Timer scheduling
- [ ] Notification system
- [ ] Auto-lock trigger

### ⏳ Phase 5: Android Overlay System

- [ ] System overlay permissions
- [ ] Full-screen lock implementation
- [ ] Emergency access whitelist
- [ ] Device wake lock management

## Technical Implementation Details

### Dependencies Installed

```json
{
  "zustand": "^4.x",
  "@tanstack/react-query": "^5.x",
  "react-native-mmkv": "^2.x",
  "expo-av": "~13.x",
  "expo-background-fetch": "~11.x",
  "expo-task-manager": "~11.x",
  "expo-keep-awake": "~12.x",
  "expo-notifications": "~0.x",
  "react-hook-form": "^7.x",
  "@hookform/resolvers": "^3.x",
  "zod": "^3.x",
  "nativewind": "^2.x",
  "react-native-reanimated": "~3.x",
  "@gorhom/bottom-sheet": "^4.x",
  "@sentry/react-native": "^5.x"
}
```

### Key Components to Build

#### 1. SoftLockTimer Component

- 15-minute countdown timer
- Visual progress indicator
- Background timer persistence
- State management with Zustand

#### 2. SoftLockScreen Component

- Full-screen overlay
- Spiritual content display
- Exit button with long-press
- Confirmation modal

#### 3. AudioService

- Adhaan playback
- Quranic verse rotation
- Background audio management
- Volume and playback controls

#### 4. PrayerTimeService

- Manual time input
- Automatic scheduling
- Notification triggers
- Background task management

#### 5. OverlayService (Android)

- System overlay permissions
- Full-screen lock implementation
- Emergency access management
- Device wake lock

## Android-Specific Considerations

### Permissions Required

```xml
<!-- app.json/expo.android.permissions -->
- SYSTEM_ALERT_WINDOW (overlay)
- WAKE_LOCK (keep screen on)
- FOREGROUND_SERVICE (background tasks)
- RECEIVE_BOOT_COMPLETED (auto-start)
- VIBRATE (notifications)
```

### Background Processing

- Use Expo Task Manager for background timers
- Implement foreground service for critical timing
- Handle app state changes gracefully
- Maintain timer accuracy during sleep

### Overlay Implementation

- Request SYSTEM_ALERT_WINDOW permission
- Create overlay view that covers system UI
- Implement emergency access patterns
- Handle navigation and back button

## Current Implementation Status

### ✅ What's Working (MVP Core Features)

#### 1. Timer System

- **15-minute countdown timer** with accurate time tracking
- **Background persistence** - timer continues when app is backgrounded
- **App state management** - handles foreground/background transitions
- **Real-time updates** with 1-second precision
- **Auto-completion** when timer reaches zero

#### 2. Soft Lock Screen

- **Full-screen modal overlay** that covers the entire app
- **Beautiful UI** with Islamic-themed design (dark theme, spiritual colors)
- **Timer display** with circular progress indicator and formatted time
- **Spiritual content** display with Quranic verses
- **Status bar integration** with proper styling

#### 3. Exit Mechanism

- **Long-press to exit** (3-second hold requirement)
- **Visual progress feedback** with animated progress bar
- **Confirmation dialog** with guilt-based messaging
- **Multiple exit attempt tracking** with escalating messages
- **Smooth animations** for all interactions

#### 4. State Management

- **Zustand store** for centralized timer state
- **Custom hooks** for timer logic and formatting
- **Reactive UI updates** based on state changes
- **Proper cleanup** and memory management

#### 5. User Experience

- **Intuitive controls** - Start, Pause, Resume, Stop
- **Visual feedback** for all actions
- **Prayer context** tracking (prayer name, start time)
- **Exit attempt monitoring** for self-reflection

### 🧪 Testing & Validation

- **Web version running** at http://localhost:8081
- **Core functionality tested** and working
- **Timer accuracy verified** in foreground/background
- **Exit flow validated** with proper confirmations
- **UI responsiveness confirmed** across different screen sizes

## Next Steps

### Immediate (Current Sprint)

1. ✅ Project setup and dependencies
2. ✅ Create basic timer component
3. ✅ Implement soft lock screen UI
4. ✅ Add exit functionality with confirmation

### Short Term

1. Audio service integration
2. Android overlay system implementation
3. Basic prayer time input
4. Notification system

### Medium Term

1. Emergency access features
2. Prayer time automation
3. Content management system
4. Performance optimization

## Development Notes

### Testing Strategy

- Unit tests for timer logic
- Integration tests for audio playback
- Manual testing on Android devices
- Background processing validation

### Performance Considerations

- Optimize timer updates (avoid excessive re-renders)
- Efficient audio loading and caching
- Battery usage optimization
- Memory management for long-running timers

### Security & Privacy

- Secure storage for user preferences
- No data collection without consent
- Local-first approach
- Minimal permissions request

## Known Challenges

### Technical Challenges

1. **Android Overlay Permissions**: Modern Android versions restrict overlay permissions
2. **Background Processing**: Battery optimization may kill background tasks
3. **Audio Continuity**: Maintaining audio during app state changes
4. **Timer Accuracy**: Ensuring precise timing during device sleep

### Solutions Approach

1. **Overlay**: Use accessibility services as fallback, request permissions properly
2. **Background**: Implement foreground service, educate users about battery settings
3. **Audio**: Use proper audio session management, handle interruptions
4. **Timer**: Use system alarms, validate timer state on app resume

## Resources & References

- [Expo Documentation](https://docs.expo.dev/)
- [React Native Reanimated](https://docs.swmansion.com/react-native-reanimated/)
- [Zustand Documentation](https://github.com/pmndrs/zustand)
- [Android Overlay Guide](https://developer.android.com/guide/topics/ui/window-overlays)
