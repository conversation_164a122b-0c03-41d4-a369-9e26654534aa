{"name": "taqwa<PERSON><PERSON>", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.6", "@hookform/resolvers": "^5.1.1", "@react-navigation/native": "^7.1.6", "@sentry/react-native": "^6.17.0", "@tanstack/react-query": "^5.82.0", "expo": "~53.0.17", "expo-av": "^15.1.7", "expo-background-fetch": "^13.1.6", "expo-font": "~13.3.2", "expo-intent-launcher": "^12.1.5", "expo-keep-awake": "^14.1.4", "expo-linking": "~7.1.7", "expo-notifications": "^0.31.4", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-task-manager": "^13.1.6", "expo-web-browser": "~14.2.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.60.0", "react-native": "0.79.5", "react-native-mmkv": "^3.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-system-setting": "^1.7.6", "react-native-web": "~0.20.0", "zod": "^4.0.3", "zustand": "^5.0.6", "expo-dev-client": "~5.2.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "jest": "^29.2.1", "jest-expo": "~53.0.9", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}