import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Modal,
  StatusBar,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import {
  useTimerStore,
  useTimerStatus,
  usePrayerContext,
} from '../store/timerStore';
import { TimerDisplay } from './TimerDisplay';
import { useTimer } from '../hooks/useTimer';

interface SoftLockScreenProps {
  visible: boolean;
  onExit?: () => void;
}

export const SoftLockScreen: React.FC<SoftLockScreenProps> = ({
  visible,
  onExit,
}) => {
  const [showExitConfirmation, setShowExitConfirmation] = useState(false);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);

  const { stopTimer, recordExitAttempt } = useTimerStore();
  const { isRunning } = useTimerStatus();
  const { currentPrayer, exitAttempts } = usePrayerContext();

  // Initialize timer hook
  useTimer();

  // Animation values
  const exitButtonScale = useSharedValue(1);
  const exitButtonOpacity = useSharedValue(0.7);
  const progressWidth = useSharedValue(0);

  const handleLongPressStart = () => {
    // Start long press animation
    exitButtonScale.value = withTiming(0.95, { duration: 100 });
    exitButtonOpacity.value = withTiming(1, { duration: 100 });

    // Start progress animation
    progressWidth.value = withTiming(100, { duration: 3000 });

    // Start timer for long press detection
    longPressTimer.current = setTimeout(() => {
      // Long press completed - show confirmation
      setShowExitConfirmation(true);
      recordExitAttempt();

      // Reset animations after showing confirmation
      exitButtonScale.value = withTiming(1, { duration: 100 });
      exitButtonOpacity.value = withTiming(0.7, { duration: 100 });
      progressWidth.value = withTiming(0, { duration: 200 });
    }, 3000);
  };

  const handleLongPressEnd = () => {
    // Only reset if timer hasn't completed yet
    if (longPressTimer.current) {
      // Reset animations
      exitButtonScale.value = withTiming(1, { duration: 100 });
      exitButtonOpacity.value = withTiming(0.7, { duration: 100 });
      progressWidth.value = withTiming(0, { duration: 200 });

      // Clear timer
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  };

  const handleConfirmExit = () => {
    setShowExitConfirmation(false);
    stopTimer();
    onExit?.();
  };

  const handleCancelExit = () => {
    setShowExitConfirmation(false);
  };

  const exitButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: exitButtonScale.value }],
    opacity: exitButtonOpacity.value,
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`,
  }));

  const getGuiltMessage = () => {
    const messages = [
      'This is your sacred time with Allah. Are you sure you want to skip it?',
      'Prayer is the pillar of faith. Consider staying for your spiritual well-being.',
      'These 15 minutes could bring you closer to Allah. Please reconsider.',
      'Your soul needs this time more than your phone does.',
      'Remember, prayer is better than sleep, and better than any distraction.',
    ];

    return messages[exitAttempts % messages.length];
  };

  if (!visible || !isRunning) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="fade"
      presentationStyle="fullScreen"
    >
      <StatusBar barStyle="light-content" backgroundColor="#1F2937" />

      <View style={styles.container}>
        {/* Background Gradient Effect */}
        <View style={styles.backgroundGradient} />

        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.prayerTitle}>
            {currentPrayer || 'Prayer Time'}
          </Text>
          <Text style={styles.subtitle}>
            Focus on your connection with Allah
          </Text>
        </View>

        {/* Timer Display */}
        <View style={styles.timerContainer}>
          <TimerDisplay
            size={250}
            strokeWidth={6}
            color="#10B981"
            backgroundColor="#374151"
            textColor="#F9FAFB"
          />
        </View>

        {/* Spiritual Content Area */}
        <View style={styles.contentContainer}>
          <Text style={styles.spiritualText}>
            "And establish prayer and give zakah and bow with those who bow."
          </Text>
          <Text style={styles.verseReference}>- Quran 2:43</Text>
        </View>

        {/* Exit Button */}
        <View style={styles.exitContainer}>
          <Animated.View style={[styles.exitButton, exitButtonAnimatedStyle]}>
            <Pressable
              onPressIn={handleLongPressStart}
              onPressOut={handleLongPressEnd}
              style={styles.exitPressable}
            >
              <Text style={styles.exitButtonText}>Hold to Exit</Text>

              {/* Progress Bar */}
              <View style={styles.progressBar}>
                <Animated.View
                  style={[styles.progressFill, progressAnimatedStyle]}
                />
              </View>

              <Text style={styles.exitHint}>Hold for 3 seconds</Text>
            </Pressable>
          </Animated.View>

          {exitAttempts > 0 && (
            <Text style={styles.attemptWarning}>
              Exit attempts: {exitAttempts}
            </Text>
          )}
        </View>

        {/* Exit Confirmation Modal */}
        <Modal visible={showExitConfirmation} transparent animationType="fade">
          <View style={styles.confirmationOverlay}>
            <View style={styles.confirmationContainer}>
              <Text style={styles.confirmationTitle}>Skip Prayer Time?</Text>

              <Text style={styles.confirmationMessage}>
                {getGuiltMessage()}
              </Text>

              <View style={styles.confirmationButtons}>
                <Pressable
                  style={[styles.confirmationButton, styles.cancelButton]}
                  onPress={handleCancelExit}
                >
                  <Text style={styles.cancelButtonText}>Stay & Pray</Text>
                </Pressable>

                <Pressable
                  style={[styles.confirmationButton, styles.exitConfirmButton]}
                  onPress={handleConfirmExit}
                >
                  <Text style={styles.exitConfirmButtonText}>Skip</Text>
                </Pressable>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1F2937',
    justifyContent: 'space-between',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#1F2937',
    opacity: 0.95,
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
  },
  prayerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#F9FAFB',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#D1D5DB',
    textAlign: 'center',
    marginTop: 8,
  },
  timerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  contentContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 40,
  },
  spiritualText: {
    fontSize: 18,
    color: '#F9FAFB',
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 26,
  },
  verseReference: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    marginTop: 8,
  },
  exitContainer: {
    alignItems: 'center',
  },
  exitButton: {
    backgroundColor: '#374151',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#4B5563',
  },
  exitPressable: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    alignItems: 'center',
    minWidth: 160,
  },
  exitButtonText: {
    color: '#F9FAFB',
    fontSize: 16,
    fontWeight: '600',
  },
  progressBar: {
    width: 120,
    height: 3,
    backgroundColor: '#4B5563',
    borderRadius: 2,
    marginTop: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#EF4444',
    borderRadius: 2,
  },
  exitHint: {
    color: '#9CA3AF',
    fontSize: 12,
    marginTop: 4,
  },
  attemptWarning: {
    color: '#F59E0B',
    fontSize: 14,
    marginTop: 12,
    textAlign: 'center',
  },
  confirmationOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  confirmationContainer: {
    backgroundColor: '#374151',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  confirmationTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#F9FAFB',
    textAlign: 'center',
    marginBottom: 16,
  },
  confirmationMessage: {
    fontSize: 16,
    color: '#D1D5DB',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  confirmationButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  confirmationButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#10B981',
  },
  exitConfirmButton: {
    backgroundColor: '#EF4444',
  },
  cancelButtonText: {
    color: '#F9FAFB',
    fontSize: 16,
    fontWeight: '600',
  },
  exitConfirmButtonText: {
    color: '#F9FAFB',
    fontSize: 16,
    fontWeight: '600',
  },
});
