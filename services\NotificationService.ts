import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

class NotificationService {
  private isInitialized = false;

  /**
   * Initialize notification service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Request permissions
      const { status } = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowAnnouncements: true,
        },
      });

      if (status !== 'granted') {
        console.warn('Notification permissions not granted');
        return;
      }

      // Configure notification behavior
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowBanner: true,
          shouldShowList: true,
          shouldPlaySound: true,
          shouldSetBadge: false,
          priority: Notifications.AndroidNotificationPriority.MAX,
        }),
      });

      // Configure Android notification channel for prayer lock
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('prayer-lock', {
          name: 'Prayer Lock',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#4F46E5',
          sound: true,
          enableVibrate: true,
          showBadge: false,
          lockscreenVisibility:
            Notifications.AndroidNotificationVisibility.PUBLIC,
        });
      }

      this.isInitialized = true;
      console.log('NotificationService initialized');
    } catch (error) {
      console.error('Failed to initialize NotificationService:', error);
    }
  }

  /**
   * Show prayer lock notification
   */
  async showPrayerLockNotification(
    title: string,
    body: string
  ): Promise<string> {
    try {
      await this.initialize();

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          sound: true,
          priority: Notifications.AndroidNotificationPriority.MAX,
          sticky: true,
          autoDismiss: false,
          ...(Platform.OS === 'android' && { channelId: 'prayer-lock' }),
        },
        trigger: null, // Show immediately
      });

      return notificationId;
    } catch (error) {
      console.error('Failed to show prayer lock notification:', error);
      return '';
    }
  }

  /**
   * Show reopen app notification
   */
  async showReopenAppNotification(): Promise<string> {
    return this.showPrayerLockNotification(
      'Return to Prayer',
      'Your prayer session is still active. Tap to return.'
    );
  }

  /**
   * Dismiss notification by ID
   */
  async dismissNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.dismissNotificationAsync(notificationId);
    } catch (error) {
      console.error('Failed to dismiss notification:', error);
    }
  }

  /**
   * Dismiss all notifications
   */
  async dismissAllNotifications(): Promise<void> {
    try {
      await Notifications.dismissAllNotificationsAsync();
    } catch (error) {
      console.error('Failed to dismiss all notifications:', error);
    }
  }
}

export const notificationService = new NotificationService();
