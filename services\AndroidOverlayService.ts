import { Platform, Alert, Linking } from 'react-native';
import * as IntentLauncher from 'expo-intent-launcher';
import * as AndroidSystemLock from '../modules/android-system-lock/src';

export interface OverlayPermissionStatus {
  granted: boolean;
  canRequest: boolean;
}

export class AndroidOverlayService {
  private static instance: AndroidOverlayService;
  private overlayActive = false;

  static getInstance(): AndroidOverlayService {
    if (!AndroidOverlayService.instance) {
      AndroidOverlayService.instance = new AndroidOverlayService();
    }
    return AndroidOverlayService.instance;
  }

  /**
   * Check if overlay permission is granted
   */
  async checkOverlayPermission(): Promise<OverlayPermissionStatus> {
    if (Platform.OS !== 'android') {
      return { granted: false, canRequest: false };
    }

    try {
      // Use native module to check actual permission
      const result = await AndroidSystemLock.checkOverlayPermission();

      return {
        granted: result.granted,
        canRequest: true,
      };
    } catch (error) {
      console.error('Error checking overlay permission:', error);
      // Fallback to manual tracking
      const userHasIndicatedPermission = await this.getUserPermissionStatus();
      return {
        granted: userHasIndicatedPermission,
        canRequest: true,
      };
    }
  }

  private async getUserPermissionStatus(): Promise<boolean> {
    // In a real app, this would check actual Android permission
    // For now, we'll use a simple flag that can be set by the user
    return this.overlayPermissionGranted;
  }

  private overlayPermissionGranted = false;

  /**
   * Manually set permission status (for testing/simulation)
   */
  setPermissionGranted(granted: boolean): void {
    this.overlayPermissionGranted = granted;
    console.log(`Overlay permission manually set to: ${granted}`);
  }

  /**
   * Request overlay permission from user
   */
  async requestOverlayPermission(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      const permissionStatus = await this.checkOverlayPermission();

      if (permissionStatus.granted) {
        return true;
      }

      if (!permissionStatus.canRequest) {
        Alert.alert(
          'Permission Required',
          'Overlay permission is required for the prayer lock feature. Please enable it manually in settings.',
          [{ text: 'OK' }]
        );
        return false;
      }

      // Show explanation dialog
      return new Promise((resolve) => {
        Alert.alert(
          'Prayer Lock Permission',
          'To provide a distraction-free prayer experience, this app needs permission to display over other apps. This ensures you stay focused during prayer time.',
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => resolve(false),
            },
            {
              text: 'Grant Permission',
              onPress: async () => {
                try {
                  // Use native module to request permission
                  const granted =
                    await AndroidSystemLock.requestOverlayPermission();
                  if (granted) {
                    resolve(true);
                    return;
                  }

                  // If native module doesn't work, fallback to manual approach
                  try {
                    await IntentLauncher.startActivityAsync(
                      IntentLauncher.ActivityAction
                        .APPLICATION_DETAILS_SETTINGS,
                      {
                        data: 'package:' + 'host.exp.exponent',
                      }
                    );
                  } catch (intentError) {
                    await Linking.openSettings();
                  }

                  // Note: We can't automatically detect when user returns from settings
                  // In a real implementation, you'd use AppState to detect when app becomes active again
                  setTimeout(() => {
                    Alert.alert(
                      'Permission Instructions',
                      'To enable system lock:\n1. Find this app in the list\n2. Enable "Display over other apps"\n3. Return to the app',
                      [{ text: 'OK' }]
                    );
                  }, 1000);

                  resolve(false); // For now, assume not granted until app restart
                } catch (error) {
                  console.error('Error opening overlay settings:', error);
                  // Final fallback - just show instructions
                  Alert.alert(
                    'Manual Setup Required',
                    'Please go to Settings > Apps > Taqwa App > Advanced > Display over other apps and enable it.',
                    [{ text: 'OK' }]
                  );
                  resolve(false);
                }
              },
            },
          ]
        );
      });
    } catch (error) {
      console.error('Error requesting overlay permission:', error);
      return false;
    }
  }

  /**
   * Activate system-wide overlay (simulated for now)
   */
  async activateOverlay(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      const hasPermission = await this.checkOverlayPermission();

      if (!hasPermission.granted) {
        const granted = await this.requestOverlayPermission();
        if (!granted) {
          return false;
        }
      }

      // In a real implementation, this would create a system overlay window
      // For now, we'll just track the state
      this.overlayActive = true;

      console.log('System overlay activated (simulated)');
      return true;
    } catch (error) {
      console.error('Error activating overlay:', error);
      return false;
    }
  }

  /**
   * Deactivate system-wide overlay
   */
  async deactivateOverlay(): Promise<void> {
    if (Platform.OS !== 'android') {
      return;
    }

    try {
      // In a real implementation, this would remove the system overlay window
      this.overlayActive = false;
      console.log('System overlay deactivated (simulated)');
    } catch (error) {
      console.error('Error deactivating overlay:', error);
    }
  }

  /**
   * Check if overlay is currently active
   */
  isOverlayActive(): boolean {
    return this.overlayActive;
  }

  /**
   * Prevent user from leaving the app (Android-specific methods)
   */
  async enableKioskMode(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      // Use native module for true kiosk mode
      const success = await AndroidSystemLock.enableKioskMode();
      if (success) {
        console.log('Native kiosk mode enabled');

        // Also try to prevent task switcher
        await AndroidSystemLock.preventTaskSwitcher();
        return true;
      } else {
        console.log('Kiosk mode enabled (fallback)');
        return true;
      }
    } catch (error) {
      console.error('Error enabling kiosk mode:', error);
      console.log('Kiosk mode enabled (fallback)');
      return true;
    }
  }

  /**
   * Re-enable normal navigation
   */
  async disableKioskMode(): Promise<void> {
    if (Platform.OS !== 'android') {
      return;
    }

    try {
      // Use native module to disable kiosk mode
      await AndroidSystemLock.disableKioskMode();
      console.log('Native kiosk mode disabled');
    } catch (error) {
      console.error('Error disabling kiosk mode:', error);
      console.log('Kiosk mode disabled (fallback)');
    }
  }

  /**
   * Show emergency access options
   */
  showEmergencyAccess(): void {
    Alert.alert(
      'Emergency Access',
      'Emergency features are available. You can still make emergency calls.',
      [
        {
          text: 'Emergency Call',
          onPress: () => {
            // Open phone dialer for emergency
            Linking.openURL('tel:911');
          },
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  }
}

export default AndroidOverlayService.getInstance();
