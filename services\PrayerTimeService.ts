import * as Location from 'expo-location';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface PrayerTimes {
  fajr: string;
  dhuhr: string;
  asr: string;
  maghrib: string;
  isha: string;
  sunrise?: string;
  sunset?: string;
}

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  city?: string;
  country?: string;
}

export interface PrayerTimeSettings {
  useAutoLocation: boolean;
  manualLocation?: LocationCoordinates;
  manualPrayerTimes?: PrayerTimes;
  calculationMethod: 'MWL' | 'ISNA' | 'Egypt' | 'Makkah' | 'Karachi' | 'Tehran' | 'Jafari';
  madhab: 'Sha<PERSON>' | 'Hanafi';
  timezone?: string;
}

class PrayerTimeService {
  private static instance: PrayerTimeService;
  private currentLocation: LocationCoordinates | null = null;
  private currentPrayerTimes: PrayerTimes | null = null;
  private settings: PrayerTimeSettings = {
    useAutoLocation: true,
    calculationMethod: 'MWL',
    madhab: '<PERSON>ha<PERSON>',
  };

  static getInstance(): PrayerTimeService {
    if (!PrayerTimeService.instance) {
      PrayerTimeService.instance = new PrayerTimeService();
    }
    return PrayerTimeService.instance;
  }

  /**
   * Initialize prayer time service
   */
  async initialize(): Promise<void> {
    try {
      await this.loadSettings();
      
      if (this.settings.useAutoLocation) {
        await this.getCurrentLocation();
        await this.calculatePrayerTimes();
      } else if (this.settings.manualPrayerTimes) {
        this.currentPrayerTimes = this.settings.manualPrayerTimes;
      }
      
      console.log('PrayerTimeService initialized');
    } catch (error) {
      console.error('Failed to initialize PrayerTimeService:', error);
    }
  }

  /**
   * Get current location using GPS
   */
  async getCurrentLocation(): Promise<LocationCoordinates | null> {
    try {
      // Request location permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission denied');
        return null;
      }

      // Get current position
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      // Get address details
      const address = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      this.currentLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        city: address[0]?.city || undefined,
        country: address[0]?.country || undefined,
      };

      await this.saveSettings();
      return this.currentLocation;
    } catch (error) {
      console.error('Failed to get current location:', error);
      return null;
    }
  }

  /**
   * Calculate prayer times based on location
   */
  async calculatePrayerTimes(date?: Date): Promise<PrayerTimes | null> {
    try {
      const location = this.currentLocation || this.settings.manualLocation;
      if (!location) {
        console.log('No location available for prayer time calculation');
        return null;
      }

      const targetDate = date || new Date();
      
      // Use Aladhan API for prayer time calculation
      const response = await fetch(
        `https://api.aladhan.com/v1/timings/${this.formatDate(targetDate)}?latitude=${location.latitude}&longitude=${location.longitude}&method=${this.getCalculationMethodNumber()}&madhab=${this.getMadhabNumber()}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch prayer times from API');
      }

      const data = await response.json();
      const timings = data.data.timings;

      this.currentPrayerTimes = {
        fajr: this.formatTime(timings.Fajr),
        dhuhr: this.formatTime(timings.Dhuhr),
        asr: this.formatTime(timings.Asr),
        maghrib: this.formatTime(timings.Maghrib),
        isha: this.formatTime(timings.Isha),
        sunrise: this.formatTime(timings.Sunrise),
        sunset: this.formatTime(timings.Sunset),
      };

      console.log('Prayer times calculated:', this.currentPrayerTimes);
      return this.currentPrayerTimes;
    } catch (error) {
      console.error('Failed to calculate prayer times:', error);
      return null;
    }
  }

  /**
   * Get current prayer times
   */
  getCurrentPrayerTimes(): PrayerTimes | null {
    return this.currentPrayerTimes;
  }

  /**
   * Get next prayer time
   */
  getNextPrayer(): { name: string; time: string; timeUntil: number } | null {
    if (!this.currentPrayerTimes) return null;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    const prayers = [
      { name: 'Fajr', time: this.currentPrayerTimes.fajr },
      { name: 'Dhuhr', time: this.currentPrayerTimes.dhuhr },
      { name: 'Asr', time: this.currentPrayerTimes.asr },
      { name: 'Maghrib', time: this.currentPrayerTimes.maghrib },
      { name: 'Isha', time: this.currentPrayerTimes.isha },
    ];

    for (const prayer of prayers) {
      const prayerTime = this.timeStringToMinutes(prayer.time);
      if (prayerTime > currentTime) {
        return {
          name: prayer.name,
          time: prayer.time,
          timeUntil: prayerTime - currentTime,
        };
      }
    }

    // If no prayer today, return Fajr tomorrow
    const fajrTime = this.timeStringToMinutes(prayers[0].time);
    return {
      name: prayers[0].name,
      time: prayers[0].time,
      timeUntil: (24 * 60) - currentTime + fajrTime,
    };
  }

  /**
   * Check if it's currently prayer time
   */
  isCurrentlyPrayerTime(): { isPrayerTime: boolean; prayerName?: string } {
    if (!this.currentPrayerTimes) return { isPrayerTime: false };

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    const prayers = [
      { name: 'Fajr', time: this.currentPrayerTimes.fajr },
      { name: 'Dhuhr', time: this.currentPrayerTimes.dhuhr },
      { name: 'Asr', time: this.currentPrayerTimes.asr },
      { name: 'Maghrib', time: this.currentPrayerTimes.maghrib },
      { name: 'Isha', time: this.currentPrayerTimes.isha },
    ];

    for (const prayer of prayers) {
      const prayerTime = this.timeStringToMinutes(prayer.time);
      // Consider it prayer time for 30 minutes after the actual time
      if (currentTime >= prayerTime && currentTime <= prayerTime + 30) {
        return { isPrayerTime: true, prayerName: prayer.name };
      }
    }

    return { isPrayerTime: false };
  }

  /**
   * Set manual prayer times
   */
  async setManualPrayerTimes(prayerTimes: PrayerTimes): Promise<void> {
    this.settings.manualPrayerTimes = prayerTimes;
    this.settings.useAutoLocation = false;
    this.currentPrayerTimes = prayerTimes;
    await this.saveSettings();
  }

  /**
   * Set manual location
   */
  async setManualLocation(location: LocationCoordinates): Promise<void> {
    this.settings.manualLocation = location;
    this.currentLocation = location;
    await this.calculatePrayerTimes();
    await this.saveSettings();
  }

  /**
   * Update prayer time settings
   */
  async updateSettings(newSettings: Partial<PrayerTimeSettings>): Promise<void> {
    this.settings = { ...this.settings, ...newSettings };
    await this.saveSettings();
    
    if (newSettings.useAutoLocation !== undefined) {
      if (newSettings.useAutoLocation) {
        await this.getCurrentLocation();
        await this.calculatePrayerTimes();
      }
    }
  }

  /**
   * Get current settings
   */
  getSettings(): PrayerTimeSettings {
    return { ...this.settings };
  }

  // Private helper methods
  private formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  }

  private formatTime(timeString: string): string {
    // Remove timezone info and return HH:MM format
    return timeString.split(' ')[0].substring(0, 5);
  }

  private timeStringToMinutes(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private getCalculationMethodNumber(): number {
    const methods = {
      'MWL': 3,      // Muslim World League
      'ISNA': 2,     // Islamic Society of North America
      'Egypt': 5,    // Egyptian General Authority of Survey
      'Makkah': 4,   // Umm Al-Qura University, Makkah
      'Karachi': 1,  // University of Islamic Sciences, Karachi
      'Tehran': 7,   // Institute of Geophysics, University of Tehran
      'Jafari': 0,   // Shia Ithna-Ashari, Leva Institute, Qum
    };
    return methods[this.settings.calculationMethod] || 3;
  }

  private getMadhabNumber(): number {
    return this.settings.madhab === 'Hanafi' ? 1 : 0;
  }

  private async saveSettings(): Promise<void> {
    try {
      await AsyncStorage.setItem('prayerTimeSettings', JSON.stringify(this.settings));
      if (this.currentLocation) {
        await AsyncStorage.setItem('currentLocation', JSON.stringify(this.currentLocation));
      }
    } catch (error) {
      console.error('Failed to save prayer time settings:', error);
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsJson = await AsyncStorage.getItem('prayerTimeSettings');
      if (settingsJson) {
        this.settings = { ...this.settings, ...JSON.parse(settingsJson) };
      }

      const locationJson = await AsyncStorage.getItem('currentLocation');
      if (locationJson) {
        this.currentLocation = JSON.parse(locationJson);
      }
    } catch (error) {
      console.error('Failed to load prayer time settings:', error);
    }
  }
}

export const prayerTimeService = PrayerTimeService.getInstance();
